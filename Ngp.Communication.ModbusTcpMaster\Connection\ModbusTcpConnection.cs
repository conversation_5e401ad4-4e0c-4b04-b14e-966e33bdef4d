using System.Net.Sockets;
using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Abstractions;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Events;
using Ngp.Communication.ModbusTcpMaster.Configuration;

namespace Ngp.Communication.ModbusTcpMaster.Connection;

/// <summary>
/// Implementation of Modbus TCP connection
/// </summary>
public class ModbusTcpConnection : IModbusConnection
{
    private readonly ILogger<ModbusTcpConnection> _logger;
    private readonly ModbusConfiguration _configuration;
    private readonly SemaphoreSlim _connectionSemaphore;
    private readonly SemaphoreSlim _requestSemaphore;
    private readonly ConcurrentDictionary<int, TaskCompletionSource<byte[]>> _pendingRequests;
    private readonly CancellationTokenSource _cancellationTokenSource;

    private TcpClient? _tcpClient;
    private NetworkStream? _networkStream;
    private ConnectionStatus _status;
    private DateTime _lastActivity;
    private bool _disposed;
    private Task? _receiveTask;

    /// <summary>
    /// Initializes a new instance of ModbusTcpConnection
    /// </summary>
    /// <param name="endpoint">The endpoint to connect to</param>
    /// <param name="configuration">Configuration options</param>
    /// <param name="logger">Logger instance</param>
    public ModbusTcpConnection(
        ModbusEndpoint endpoint,
        ModbusConfiguration configuration,
        ILogger<ModbusTcpConnection> logger)
    {
        Endpoint = endpoint ?? throw new ArgumentNullException(nameof(endpoint));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _connectionSemaphore = new SemaphoreSlim(1, 1);
        _requestSemaphore = new SemaphoreSlim(_configuration.MaxConcurrentRequestsPerConnection, _configuration.MaxConcurrentRequestsPerConnection);
        _pendingRequests = new ConcurrentDictionary<int, TaskCompletionSource<byte[]>>();
        _cancellationTokenSource = new CancellationTokenSource();

        _status = ConnectionStatus.Disconnected;
        _lastActivity = DateTime.UtcNow;
    }

    /// <summary>
    /// Gets the endpoint this connection is associated with
    /// </summary>
    public ModbusEndpoint Endpoint { get; }

    /// <summary>
    /// Gets the current connection status
    /// </summary>
    public ConnectionStatus Status => _status;

    /// <summary>
    /// Gets whether the connection is currently connected
    /// </summary>
    public bool IsConnected => _status == ConnectionStatus.Connected && _tcpClient?.Connected == true;

    /// <summary>
    /// Gets the last activity timestamp
    /// </summary>
    public DateTime LastActivity => _lastActivity;

    /// <summary>
    /// Event triggered when connection status changes
    /// </summary>
    public event EventHandler<ConnectionStatusEventArgs>? StatusChanged;

    /// <summary>
    /// Connects to the Modbus slave
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if connection successful, false otherwise</returns>
    public async Task<bool> ConnectAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(ModbusTcpConnection));
        }

        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (IsConnected)
            {
                return true;
            }

            SetStatus(ConnectionStatus.Connecting);

            try
            {
                _tcpClient = new TcpClient();
                ConfigureTcpClient(_tcpClient);

                using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                timeoutCts.CancelAfter(_configuration.DefaultConnectionTimeoutMs);

                await _tcpClient.ConnectAsync(Endpoint.IpAddress, Endpoint.Port, timeoutCts.Token);
                _networkStream = _tcpClient.GetStream();

                SetStatus(ConnectionStatus.Connected);
                _lastActivity = DateTime.UtcNow;

                // Start receive task
                _receiveTask = Task.Run(() => ReceiveLoop(_cancellationTokenSource.Token), _cancellationTokenSource.Token);

                _logger.LogInformation("Connected to {Endpoint}", Endpoint);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to connect to {Endpoint}", Endpoint);
                SetStatus(ConnectionStatus.Error);
                await CleanupConnectionAsync();
                return false;
            }
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    /// <summary>
    /// Disconnects from the Modbus slave
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the disconnection operation</returns>
    public async Task DisconnectAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            return;
        }

        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (_status == ConnectionStatus.Disconnected)
            {
                return;
            }

            SetStatus(ConnectionStatus.Disconnecting);
            await CleanupConnectionAsync();
            SetStatus(ConnectionStatus.Disconnected);

            _logger.LogInformation("Disconnected from {Endpoint}", Endpoint);
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    /// <summary>
    /// Sends a Modbus request and waits for response
    /// </summary>
    /// <param name="request">The request to send</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The response from the slave</returns>
    public async Task<byte[]> SendRequestAsync(byte[] request, CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(ModbusTcpConnection));
        }

        if (!IsConnected)
        {
            throw new InvalidOperationException("Connection is not established");
        }

        await _requestSemaphore.WaitAsync(cancellationToken);
        try
        {
            var requestId = GetRequestId(request);
            var tcs = new TaskCompletionSource<byte[]>();
            _pendingRequests[requestId] = tcs;

            try
            {
                await _networkStream!.WriteAsync(request, cancellationToken);
                _lastActivity = DateTime.UtcNow;

                using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                timeoutCts.CancelAfter(_configuration.DefaultRequestTimeoutMs);

                var response = await tcs.Task.WaitAsync(timeoutCts.Token);
                _lastActivity = DateTime.UtcNow;

                return response;
            }
            finally
            {
                _pendingRequests.TryRemove(requestId, out _);
            }
        }
        finally
        {
            _requestSemaphore.Release();
        }
    }

    /// <summary>
    /// Checks if the connection is healthy
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if connection is healthy, false otherwise</returns>
    public Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed || !IsConnected)
        {
            return Task.FromResult(false);
        }

        try
        {
            // Simple health check - verify the socket is still connected
            var isHealthy = _tcpClient?.Connected == true && _networkStream?.CanRead == true && _networkStream?.CanWrite == true;
            return Task.FromResult(isHealthy);
        }
        catch
        {
            return Task.FromResult(false);
        }
    }

    /// <summary>
    /// Disposes the connection and releases resources
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
        {
            return;
        }

        _disposed = true;

        try
        {
            _cancellationTokenSource.Cancel();
            CleanupConnectionAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during disposal of connection to {Endpoint}", Endpoint);
        }
        finally
        {
            _connectionSemaphore.Dispose();
            _requestSemaphore.Dispose();
            _cancellationTokenSource.Dispose();
        }
    }

    /// <summary>
    /// Sets the connection status and raises the StatusChanged event
    /// </summary>
    /// <param name="newStatus">The new status</param>
    private void SetStatus(ConnectionStatus newStatus)
    {
        var previousStatus = _status;
        _status = newStatus;

        if (previousStatus != newStatus)
        {
            var eventArgs = new ConnectionStatusEventArgs
            {
                Endpoint = Endpoint,
                PreviousStatus = previousStatus,
                CurrentStatus = newStatus
            };

            StatusChanged?.Invoke(this, eventArgs);
        }
    }

    /// <summary>
    /// Configures the TCP client with optimal settings
    /// </summary>
    /// <param name="tcpClient">The TCP client to configure</param>
    private void ConfigureTcpClient(TcpClient tcpClient)
    {
        tcpClient.ReceiveBufferSize = _configuration.TcpSocket.ReceiveBufferSize;
        tcpClient.SendBufferSize = _configuration.TcpSocket.SendBufferSize;
        tcpClient.NoDelay = _configuration.TcpSocket.NoDelay;

        if (_configuration.TcpSocket.LingerTimeSeconds >= 0)
        {
            tcpClient.LingerState = new LingerOption(true, _configuration.TcpSocket.LingerTimeSeconds);
        }
    }

    /// <summary>
    /// Cleans up the connection resources
    /// </summary>
    private async Task CleanupConnectionAsync()
    {
        // Cancel all pending requests
        foreach (var kvp in _pendingRequests)
        {
            kvp.Value.TrySetCanceled();
        }
        _pendingRequests.Clear();

        // Close network stream
        if (_networkStream != null)
        {
            try
            {
                await _networkStream.DisposeAsync();
            }
            catch { }
            _networkStream = null;
        }

        // Close TCP client
        if (_tcpClient != null)
        {
            try
            {
                _tcpClient.Close();
                _tcpClient.Dispose();
            }
            catch { }
            _tcpClient = null;
        }

        // Wait for receive task to complete
        if (_receiveTask != null)
        {
            try
            {
                await _receiveTask;
            }
            catch { }
            _receiveTask = null;
        }
    }

    /// <summary>
    /// Gets a request ID from the request bytes (for matching responses)
    /// </summary>
    /// <param name="request">The request bytes</param>
    /// <returns>Request ID</returns>
    private static int GetRequestId(byte[] request)
    {
        // For Modbus TCP, use transaction ID
        if (request.Length >= 2)
        {
            return (request[0] << 8) | request[1];
        }

        // For RTU over TCP, use a hash of the request
        return request.GetHashCode();
    }

    /// <summary>
    /// Continuous receive loop for handling responses
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    private async Task ReceiveLoop(CancellationToken cancellationToken)
    {
        var buffer = new byte[1024];

        try
        {
            while (!cancellationToken.IsCancellationRequested && IsConnected)
            {
                var bytesRead = await _networkStream!.ReadAsync(buffer, cancellationToken);
                if (bytesRead == 0)
                {
                    // Connection closed by remote
                    break;
                }

                var response = new byte[bytesRead];
                Array.Copy(buffer, 0, response, 0, bytesRead);

                ProcessResponse(response);
            }
        }
        catch (Exception ex) when (!cancellationToken.IsCancellationRequested)
        {
            _logger.LogError(ex, "Error in receive loop for {Endpoint}", Endpoint);
            SetStatus(ConnectionStatus.Error);
        }
    }

    /// <summary>
    /// Processes a received response
    /// </summary>
    /// <param name="response">The response bytes</param>
    private void ProcessResponse(byte[] response)
    {
        try
        {
            var requestId = GetRequestId(response);
            if (_pendingRequests.TryRemove(requestId, out var tcs))
            {
                tcs.SetResult(response);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing response from {Endpoint}", Endpoint);
        }
    }
}
