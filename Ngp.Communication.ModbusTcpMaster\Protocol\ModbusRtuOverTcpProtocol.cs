using Ngp.Communication.ModbusTcpMaster.Models;

namespace Ngp.Communication.ModbusTcpMaster.Protocol;

/// <summary>
/// Implementation of Modbus RTU over TCP protocol
/// </summary>
public class ModbusRtuOverTcpProtocol : ModbusProtocol
{
    /// <summary>
    /// Creates a read request message for Modbus RTU over TCP
    /// </summary>
    /// <param name="request">The read request</param>
    /// <param name="transactionId">Transaction ID for the request (not used in RTU)</param>
    /// <returns>The message bytes</returns>
    public override byte[] CreateReadRequest(ModbusReadRequest request, ushort transactionId)
    {
        var functionCode = GetFunctionCode(request.RegisterType);
        var pdu = CreateReadPdu(functionCode, request.StartAddress, request.Count);
        return CreateRtuFrame(request.SlaveId, pdu);
    }

    /// <summary>
    /// Creates a single write request message for Modbus RTU over TCP
    /// </summary>
    /// <param name="request">The write request</param>
    /// <param name="transactionId">Transaction ID for the request (not used in RTU)</param>
    /// <returns>The message bytes</returns>
    public override byte[] CreateWriteSingleRequest<T>(ModbusWriteSingleRequest<T> request, ushort transactionId)
    {
        var functionCode = GetFunctionCode(request.RegisterType, isWrite: true, isMultiple: false);
        byte[] pdu;

        if (typeof(T) == typeof(bool))
        {
            var value = (bool)(object)request.Value!;
            pdu = CreateWriteSingleCoilPdu(functionCode, request.Address, value);
        }
        else if (typeof(T) == typeof(ushort))
        {
            var value = (ushort)(object)request.Value!;
            pdu = CreateWriteSingleRegisterPdu(functionCode, request.Address, value);
        }
        else
        {
            throw new ArgumentException($"Unsupported value type: {typeof(T)}");
        }

        return CreateRtuFrame(request.SlaveId, pdu);
    }

    /// <summary>
    /// Creates a multiple write request message for Modbus RTU over TCP
    /// </summary>
    /// <param name="request">The write request</param>
    /// <param name="transactionId">Transaction ID for the request (not used in RTU)</param>
    /// <returns>The message bytes</returns>
    public override byte[] CreateWriteMultipleRequest<T>(ModbusWriteMultipleRequest<T> request, ushort transactionId)
    {
        var functionCode = GetFunctionCode(request.RegisterType, isWrite: true, isMultiple: true);
        byte[] pdu;

        if (typeof(T) == typeof(bool))
        {
            var values = (bool[])(object)request.Values;
            pdu = CreateWriteMultipleCoilsPdu(functionCode, request.StartAddress, values);
        }
        else if (typeof(T) == typeof(ushort))
        {
            var values = (ushort[])(object)request.Values;
            pdu = CreateWriteMultipleRegistersPdu(functionCode, request.StartAddress, values);
        }
        else
        {
            throw new ArgumentException($"Unsupported value type: {typeof(T)}");
        }

        return CreateRtuFrame(request.SlaveId, pdu);
    }

    /// <summary>
    /// Parses a Modbus RTU over TCP response message
    /// </summary>
    /// <param name="response">The response bytes</param>
    /// <param name="expectedTransactionId">Expected transaction ID (not used in RTU)</param>
    /// <returns>Parsed response data</returns>
    public override ModbusResponseData ParseResponse(byte[] response, ushort expectedTransactionId)
    {
        if (response.Length < 4) // Minimum: slave ID + function code + CRC (2 bytes)
        {
            throw new InvalidOperationException("Response too short for Modbus RTU");
        }

        // Verify CRC
        var crcReceived = FromBigEndianBytes(response, response.Length - 2);
        var crcCalculated = CalculateCrc16(response, 0, response.Length - 2);

        if (crcReceived != crcCalculated)
        {
            throw new InvalidOperationException($"CRC mismatch. Expected: 0x{crcCalculated:X4}, Received: 0x{crcReceived:X4}");
        }

        var unitId = response[0];
        var functionCode = response[1];
        var isError = ModbusFunctionCodes.IsErrorResponse(functionCode);
        byte? exceptionCode = null;
        byte[]? data = null;

        if (isError)
        {
            if (response.Length >= 5) // Slave ID + error function code + exception code + CRC
            {
                exceptionCode = response[2];
            }
        }
        else
        {
            var dataLength = response.Length - 4; // Subtract slave ID, function code, and CRC
            if (dataLength > 0)
            {
                data = new byte[dataLength];
                Array.Copy(response, 2, data, 0, dataLength);
            }
        }

        return new ModbusResponseData
        {
            TransactionId = 0, // RTU doesn't use transaction ID
            UnitId = unitId,
            FunctionCode = functionCode,
            IsError = isError,
            ExceptionCode = exceptionCode,
            Data = data,
            RawResponse = response
        };
    }

    /// <summary>
    /// Validates if the Modbus RTU over TCP response is complete
    /// </summary>
    /// <param name="response">The response bytes</param>
    /// <returns>True if complete, false otherwise</returns>
    public override bool IsResponseComplete(byte[] response)
    {
        if (response.Length < 4)
        {
            return false;
        }

        // For RTU, we need to determine the expected length based on the function code
        var functionCode = response[1];

        if (ModbusFunctionCodes.IsErrorResponse(functionCode))
        {
            return response.Length >= 5; // Slave ID + error function code + exception code + CRC
        }

        // For read responses, the byte count is in the third byte
        if (functionCode == ModbusFunctionCodes.ReadCoils ||
            functionCode == ModbusFunctionCodes.ReadDiscreteInputs ||
            functionCode == ModbusFunctionCodes.ReadHoldingRegisters ||
            functionCode == ModbusFunctionCodes.ReadInputRegisters)
        {
            if (response.Length < 3)
            {
                return false;
            }

            var byteCount = response[2];
            var expectedLength = 3 + byteCount + 2; // Slave ID + function code + byte count + data + CRC
            return response.Length >= expectedLength;
        }

        // For write responses, the length is fixed
        if (functionCode == ModbusFunctionCodes.WriteSingleCoil ||
            functionCode == ModbusFunctionCodes.WriteSingleRegister ||
            functionCode == ModbusFunctionCodes.WriteMultipleCoils ||
            functionCode == ModbusFunctionCodes.WriteMultipleRegisters)
        {
            return response.Length >= 8; // Slave ID + function code + address (2) + value/count (2) + CRC (2)
        }

        // For unknown function codes, assume minimum length
        return response.Length >= 4;
    }

    /// <summary>
    /// Gets the expected response length for a Modbus RTU read request
    /// </summary>
    /// <param name="request">The request</param>
    /// <returns>Expected response length in bytes</returns>
    public override int GetExpectedResponseLength(ModbusReadRequest request)
    {
        int dataLength = request.RegisterType switch
        {
            ModbusRegisterType.Coil or ModbusRegisterType.DiscreteInput => 1 + (request.Count + 7) / 8, // Byte count + data
            ModbusRegisterType.HoldingRegister or ModbusRegisterType.InputRegister => 1 + (request.Count * 2), // Byte count + data
            _ => throw new ArgumentException($"Invalid register type: {request.RegisterType}")
        };

        return 2 + dataLength + 2; // Slave ID + function code + data + CRC
    }

    /// <summary>
    /// Creates RTU frame with CRC
    /// </summary>
    /// <param name="slaveId">Slave identifier</param>
    /// <param name="pdu">Protocol data unit</param>
    /// <returns>Complete RTU frame with CRC</returns>
    private static byte[] CreateRtuFrame(byte slaveId, byte[] pdu)
    {
        var frame = new byte[1 + pdu.Length + 2]; // Slave ID + PDU + CRC
        frame[0] = slaveId;
        Array.Copy(pdu, 0, frame, 1, pdu.Length);

        var crc = CalculateCrc16(frame, 0, frame.Length - 2);
        var crcBytes = ToBigEndianBytes(crc);
        frame[frame.Length - 2] = crcBytes[1]; // CRC low byte first in RTU
        frame[frame.Length - 1] = crcBytes[0]; // CRC high byte second in RTU

        return frame;
    }

    /// <summary>
    /// Calculates CRC-16 for Modbus RTU
    /// </summary>
    /// <param name="data">Data to calculate CRC for</param>
    /// <param name="offset">Starting offset</param>
    /// <param name="length">Length of data</param>
    /// <returns>CRC-16 value</returns>
    private static ushort CalculateCrc16(byte[] data, int offset, int length)
    {
        ushort crc = 0xFFFF;

        for (int i = offset; i < offset + length; i++)
        {
            crc ^= data[i];

            for (int j = 0; j < 8; j++)
            {
                if ((crc & 0x0001) != 0)
                {
                    crc >>= 1;
                    crc ^= 0xA001;
                }
                else
                {
                    crc >>= 1;
                }
            }
        }

        return crc;
    }

    // Helper methods for creating PDUs (similar to ModbusTcpProtocol but without MBAP header)
    private static byte[] CreateReadPdu(byte functionCode, ushort startAddress, ushort count)
    {
        var pdu = new byte[5];
        pdu[0] = functionCode;

        var addressBytes = ToBigEndianBytes(startAddress);
        pdu[1] = addressBytes[0];
        pdu[2] = addressBytes[1];

        var countBytes = ToBigEndianBytes(count);
        pdu[3] = countBytes[0];
        pdu[4] = countBytes[1];

        return pdu;
    }

    private static byte[] CreateWriteSingleCoilPdu(byte functionCode, ushort address, bool value)
    {
        var pdu = new byte[5];
        pdu[0] = functionCode;

        var addressBytes = ToBigEndianBytes(address);
        pdu[1] = addressBytes[0];
        pdu[2] = addressBytes[1];

        pdu[3] = value ? (byte)0xFF : (byte)0x00;
        pdu[4] = 0x00;

        return pdu;
    }

    private static byte[] CreateWriteSingleRegisterPdu(byte functionCode, ushort address, ushort value)
    {
        var pdu = new byte[5];
        pdu[0] = functionCode;

        var addressBytes = ToBigEndianBytes(address);
        pdu[1] = addressBytes[0];
        pdu[2] = addressBytes[1];

        var valueBytes = ToBigEndianBytes(value);
        pdu[3] = valueBytes[0];
        pdu[4] = valueBytes[1];

        return pdu;
    }

    private static byte[] CreateWriteMultipleCoilsPdu(byte functionCode, ushort startAddress, bool[] values)
    {
        var packedValues = PackBooleans(values);
        var pdu = new byte[6 + packedValues.Length];

        pdu[0] = functionCode;

        var addressBytes = ToBigEndianBytes(startAddress);
        pdu[1] = addressBytes[0];
        pdu[2] = addressBytes[1];

        var countBytes = ToBigEndianBytes((ushort)values.Length);
        pdu[3] = countBytes[0];
        pdu[4] = countBytes[1];

        pdu[5] = (byte)packedValues.Length;

        Array.Copy(packedValues, 0, pdu, 6, packedValues.Length);

        return pdu;
    }

    private static byte[] CreateWriteMultipleRegistersPdu(byte functionCode, ushort startAddress, ushort[] values)
    {
        var pdu = new byte[6 + (values.Length * 2)];

        pdu[0] = functionCode;

        var addressBytes = ToBigEndianBytes(startAddress);
        pdu[1] = addressBytes[0];
        pdu[2] = addressBytes[1];

        var countBytes = ToBigEndianBytes((ushort)values.Length);
        pdu[3] = countBytes[0];
        pdu[4] = countBytes[1];

        pdu[5] = (byte)(values.Length * 2);

        for (int i = 0; i < values.Length; i++)
        {
            var valueBytes = ToBigEndianBytes(values[i]);
            pdu[6 + (i * 2)] = valueBytes[0];
            pdu[7 + (i * 2)] = valueBytes[1];
        }

        return pdu;
    }
}
