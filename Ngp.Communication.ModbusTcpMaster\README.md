# Ngp.Communication.ModbusTcpMaster

A high-performance, thread-safe Modbus TCP Master library for .NET 9 with support for up to 1000+ concurrent connections.

## Features

- ✅ **High Performance**: Supports 1000+ concurrent connections with parallel processing
- ✅ **Thread-Safe**: All operations are thread-safe for concurrent access
- ✅ **Protocol Support**: ModbusTCP and Modbus RTU over TCP
- ✅ **Complete Function Support**: All standard Modbus function codes
- ✅ **Automatic Optimization**: Intelligent register range optimization
- ✅ **Event-Driven**: Comprehensive event system with parallel processing
- ✅ **Connection Management**: Automatic connection pooling and health monitoring
- ✅ **Retry Logic**: Configurable connection retry with exponential backoff
- ✅ **FluentAPI**: Easy-to-use fluent interface for building requests
- ✅ **Continuous Polling**: Automatic data polling with configurable intervals
- ✅ **Error Handling**: Comprehensive error detection and reporting
- ✅ **Resource Management**: Proper resource cleanup and disposal

## Quick Start

### Basic Usage

```csharp
using Ngp.Communication.ModbusTcpMaster.FluentApi;
using Ngp.Communication.ModbusTcpMaster.Models;

// Create a Modbus master
using var master = ModbusMaster.CreateDefault();

// Define endpoint
var endpoint = ModbusEndpoint.Create("*************", 502);

// Connect to device
await master.ConnectAsync(endpoint);

// Read holding registers using fluent API
var request = ModbusRequest
    .ForSlave(endpoint, slaveId: 1)
    .ReadHoldingRegisters(startAddress: 0, count: 10);

var response = await master.ReadHoldingRegistersAsync(request);

if (response.IsSuccess && response.Data != null)
{
    Console.WriteLine($"Read {response.Data.Length} registers");
    foreach (var value in response.Data)
    {
        Console.WriteLine($"Value: {value}");
    }
}
```

### High-Performance Configuration

```csharp
// Create high-performance master for many connections
using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
using var master = ModbusMaster.CreateHighPerformance(
    maxConnections: 1000, 
    loggerFactory);

// Subscribe to events
master.DataUpdated += (sender, e) => 
{
    Console.WriteLine($"Data updated: {e.Endpoint}");
};
```

### Custom Configuration

```csharp
var master = ModbusMaster.Create(builder => builder
    .WithTimeouts(connectionTimeoutMs: 10000, requestTimeoutMs: 8000)
    .WithMaxConnections(500)
    .WithParallelProcessing(true)
    .WithConnectionRetry(retry =>
    {
        retry.MaxRetryAttempts = 5;
        retry.UseExponentialBackoff = true;
    })
    .WithRegisterOptimization(opt =>
    {
        opt.EnableRangeOptimization = true;
        opt.MaxRegisterGap = 20;
    }));
```

### Continuous Polling

```csharp
// Define register ranges to poll
var registerRanges = new[]
{
    new RegisterRange
    {
        RegisterType = ModbusRegisterType.HoldingRegister,
        StartAddress = 0,
        Count = 10,
        SlaveId = 1,
        PollingIntervalMs = 1000
    }
};

// Start polling
await master.StartPollingAsync(endpoint, registerRanges);

// Subscribe to data updates
master.DataUpdated += (sender, e) =>
{
    Console.WriteLine($"Updated: {e.RegisterType} at {e.StartAddress}");
};
```

## Supported Operations

### Read Operations
- Read Coils (0x01)
- Read Discrete Inputs (0x02)
- Read Holding Registers (0x03)
- Read Input Registers (0x04)

### Write Operations
- Write Single Coil (0x05)
- Write Single Register (0x06)
- Write Multiple Coils (0x0F)
- Write Multiple Registers (0x10)

## FluentAPI Examples

### Reading Data

```csharp
// Read coils
var coilRequest = ModbusRequest
    .ForSlave("*************", 502, slaveId: 1)
    .WithTimeout(5000)
    .ReadCoils(startAddress: 0, count: 16);

// Read holding registers
var registerRequest = ModbusRequest
    .ForSlave(endpoint, slaveId: 1)
    .UsingProtocol(ModbusProtocolType.ModbusRtuOverTcp)
    .ReadHoldingRegisters(startAddress: 100, count: 10);
```

### Writing Data

```csharp
// Write single coil
var writeCoilRequest = ModbusRequest
    .ForSlave(endpoint, slaveId: 1)
    .WriteSingleCoil(address: 0, value: true);

// Write multiple registers
var writeRegistersRequest = ModbusRequest
    .ForSlave(endpoint, slaveId: 1)
    .WriteMultipleRegisters(startAddress: 0, values: new ushort[] { 100, 200, 300 });
```

## Event Handling

```csharp
// Connection status changes
master.ConnectionStatusChanged += (sender, e) =>
{
    Console.WriteLine($"Connection {e.Endpoint}: {e.PreviousStatus} -> {e.CurrentStatus}");
};

// Data updates from polling
master.DataUpdated += (sender, e) =>
{
    Console.WriteLine($"Data updated: {e.Endpoint}, Slave {e.SlaveId}");
};

// Error handling
master.ErrorOccurred += (sender, e) =>
{
    Console.WriteLine($"Error: {e.ErrorType} - {e.Message}");
};
```

## Configuration Options

### Connection Settings
- Connection timeout
- Request timeout
- Request gap time
- Maximum concurrent connections
- Maximum requests per connection

### Retry Configuration
- Maximum retry attempts
- Initial retry delay
- Exponential backoff
- Maximum retry delay

### Register Optimization
- Range optimization
- Maximum register gap
- Maximum registers per request
- Protocol-specific limits

### TCP Socket Settings
- Buffer sizes
- Keep-alive settings
- Nagle algorithm control
- Linger options

### Event Processing
- Parallel event processing
- Event queue size
- Processing threads
- Processing timeout

## Performance Characteristics

- **Concurrent Connections**: Up to 1000+ simultaneous connections
- **Parallel Processing**: Multiple requests per connection
- **Memory Efficient**: Optimized memory usage with connection pooling
- **Low Latency**: Minimal overhead with direct TCP communication
- **High Throughput**: Parallel request processing and event handling

## Thread Safety

All public methods and properties are thread-safe. The library uses:
- Concurrent collections for thread-safe data structures
- Semaphores for connection and request limiting
- Channels for high-performance event processing
- Proper disposal patterns for resource management

## Error Handling

The library provides comprehensive error handling:
- Connection errors with automatic retry
- Protocol errors with detailed exception codes
- Timeout handling with configurable timeouts
- CRC validation for RTU over TCP
- Network error detection and recovery

## Best Practices

1. **Use connection pooling** for multiple devices
2. **Enable parallel processing** for high throughput
3. **Configure appropriate timeouts** for your network
4. **Use register optimization** for efficient polling
5. **Subscribe to events** for real-time monitoring
6. **Properly dispose** of master instances
7. **Handle errors gracefully** with retry logic

## Requirements

- .NET 9.0 or later
- Network connectivity to Modbus TCP devices
- Appropriate firewall configuration for TCP connections

## License

This library is part of the Next Generation Platform (NGP) project.
