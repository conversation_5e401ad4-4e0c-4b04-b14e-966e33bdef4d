using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Configuration;

namespace Ngp.Communication.ModbusTcpMaster.Engine;

/// <summary>
/// Optimizes register read requests by combining adjacent ranges and respecting Modbus limits
/// </summary>
public class RegisterOptimizer
{
    private readonly RegisterOptimizationConfiguration _configuration;

    /// <summary>
    /// Initializes a new instance of RegisterOptimizer
    /// </summary>
    /// <param name="configuration">Optimization configuration</param>
    public RegisterOptimizer(RegisterOptimizationConfiguration configuration)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
    }

    /// <summary>
    /// Optimizes a collection of register ranges by combining adjacent ranges
    /// </summary>
    /// <param name="ranges">The register ranges to optimize</param>
    /// <returns>Optimized register ranges</returns>
    public IEnumerable<RegisterRange> OptimizeRanges(IEnumerable<RegisterRange> ranges)
    {
        if (!_configuration.EnableRangeOptimization)
        {
            return ranges;
        }

        var rangesList = ranges.ToList();
        if (rangesList.Count <= 1)
        {
            return rangesList;
        }

        // Group by register type and slave ID
        var groupedRanges = rangesList
            .GroupBy(r => new { r.RegisterType, r.SlaveId })
            .ToList();

        var optimizedRanges = new List<RegisterRange>();

        foreach (var group in groupedRanges)
        {
            var sortedRanges = group
                .OrderBy(r => r.StartAddress)
                .ToList();

            optimizedRanges.AddRange(OptimizeRangesForType(sortedRanges, group.Key.RegisterType));
        }

        return optimizedRanges;
    }

    /// <summary>
    /// Splits a register range into multiple ranges if it exceeds Modbus limits
    /// </summary>
    /// <param name="range">The register range to split</param>
    /// <returns>Split register ranges</returns>
    public IEnumerable<RegisterRange> SplitRangeIfNeeded(RegisterRange range)
    {
        var maxCount = GetMaxCountForRegisterType(range.RegisterType);

        if (range.Count <= maxCount)
        {
            return [range];
        }

        var splitRanges = new List<RegisterRange>();
        var currentAddress = range.StartAddress;
        var remainingCount = range.Count;

        while (remainingCount > 0)
        {
            var currentCount = Math.Min(remainingCount, maxCount);

            splitRanges.Add(range with
            {
                StartAddress = currentAddress,
                Count = (ushort)currentCount
            });

            currentAddress += (ushort)currentCount;
            remainingCount -= currentCount;
        }

        return splitRanges;
    }

    /// <summary>
    /// Creates optimized read requests from register ranges
    /// </summary>
    /// <param name="endpoint">The endpoint to read from</param>
    /// <param name="ranges">The register ranges</param>
    /// <param name="protocolType">The protocol type to use</param>
    /// <param name="timeoutMs">Request timeout in milliseconds</param>
    /// <returns>Optimized read requests</returns>
    public IEnumerable<ModbusReadRequest> CreateOptimizedReadRequests(
        ModbusEndpoint endpoint,
        IEnumerable<RegisterRange> ranges,
        ModbusProtocolType protocolType = ModbusProtocolType.ModbusTcp,
        int timeoutMs = 5000)
    {
        var optimizedRanges = OptimizeRanges(ranges);
        var requests = new List<ModbusReadRequest>();

        foreach (var range in optimizedRanges)
        {
            var splitRanges = SplitRangeIfNeeded(range);

            foreach (var splitRange in splitRanges)
            {
                requests.Add(new ModbusReadRequest
                {
                    Endpoint = endpoint,
                    SlaveId = splitRange.SlaveId,
                    StartAddress = splitRange.StartAddress,
                    Count = splitRange.Count,
                    RegisterType = splitRange.RegisterType,
                    ProtocolType = protocolType,
                    TimeoutMs = timeoutMs
                });
            }
        }

        return requests;
    }

    /// <summary>
    /// Optimizes ranges for a specific register type
    /// </summary>
    /// <param name="sortedRanges">Sorted register ranges of the same type</param>
    /// <param name="registerType">The register type</param>
    /// <returns>Optimized ranges</returns>
    private IEnumerable<RegisterRange> OptimizeRangesForType(
        List<RegisterRange> sortedRanges,
        ModbusRegisterType registerType)
    {
        if (sortedRanges.Count == 0)
        {
            return sortedRanges;
        }

        var optimizedRanges = new List<RegisterRange>();
        var currentRange = sortedRanges[0];

        for (int i = 1; i < sortedRanges.Count; i++)
        {
            var nextRange = sortedRanges[i];

            // Check if ranges can be combined
            if (CanCombineRanges(currentRange, nextRange, registerType))
            {
                // Combine ranges
                var endAddress = Math.Max(
                    currentRange.StartAddress + currentRange.Count - 1,
                    nextRange.StartAddress + nextRange.Count - 1);

                currentRange = currentRange with
                {
                    Count = (ushort)(endAddress - currentRange.StartAddress + 1),
                    PollingIntervalMs = Math.Min(currentRange.PollingIntervalMs, nextRange.PollingIntervalMs)
                };
            }
            else
            {
                // Cannot combine, add current range and start new one
                optimizedRanges.Add(currentRange);
                currentRange = nextRange;
            }
        }

        // Add the last range
        optimizedRanges.Add(currentRange);

        return optimizedRanges;
    }

    /// <summary>
    /// Checks if two register ranges can be combined
    /// </summary>
    /// <param name="range1">First range</param>
    /// <param name="range2">Second range</param>
    /// <param name="registerType">Register type</param>
    /// <returns>True if ranges can be combined</returns>
    private bool CanCombineRanges(RegisterRange range1, RegisterRange range2, ModbusRegisterType registerType)
    {
        // Must be same register type and slave ID
        if (range1.RegisterType != range2.RegisterType || range1.SlaveId != range2.SlaveId)
        {
            return false;
        }

        // Calculate gap between ranges
        var range1End = range1.StartAddress + range1.Count - 1;
        var range2Start = range2.StartAddress;

        if (range2Start <= range1End)
        {
            // Ranges overlap or are adjacent
            return true;
        }

        var gap = range2Start - range1End - 1;
        if (gap > _configuration.MaxRegisterGap)
        {
            // Gap is too large
            return false;
        }

        // Check if combined range would exceed limits
        var combinedCount = range2.StartAddress + range2.Count - range1.StartAddress;
        var maxCount = GetMaxCountForRegisterType(registerType);

        return combinedCount <= maxCount;
    }

    /// <summary>
    /// Gets the maximum count for a register type according to Modbus specification
    /// </summary>
    /// <param name="registerType">The register type</param>
    /// <returns>Maximum count</returns>
    private ushort GetMaxCountForRegisterType(ModbusRegisterType registerType)
    {
        return registerType switch
        {
            ModbusRegisterType.Coil => _configuration.MaxCoilsPerRequest,
            ModbusRegisterType.DiscreteInput => _configuration.MaxDiscreteInputsPerRequest,
            ModbusRegisterType.HoldingRegister => _configuration.MaxHoldingRegistersPerRequest,
            ModbusRegisterType.InputRegister => _configuration.MaxInputRegistersPerRequest,
            _ => throw new ArgumentException($"Unknown register type: {registerType}")
        };
    }
}

/// <summary>
/// Represents an optimized register read operation
/// </summary>
public class OptimizedRegisterRead
{
    /// <summary>
    /// The original register ranges that this read covers
    /// </summary>
    public required List<RegisterRange> OriginalRanges { get; init; }

    /// <summary>
    /// The optimized read request
    /// </summary>
    public required ModbusReadRequest ReadRequest { get; init; }

    /// <summary>
    /// Maps original range start addresses to their positions in the optimized read
    /// </summary>
    public required Dictionary<ushort, int> AddressMapping { get; init; }
}

/// <summary>
/// Helper class for creating address mappings
/// </summary>
public static class AddressMappingHelper
{
    /// <summary>
    /// Creates address mapping for optimized reads
    /// </summary>
    /// <param name="originalRanges">Original register ranges</param>
    /// <param name="optimizedStartAddress">Start address of optimized read</param>
    /// <returns>Address mapping dictionary</returns>
    public static Dictionary<ushort, int> CreateAddressMapping(
        IEnumerable<RegisterRange> originalRanges,
        ushort optimizedStartAddress)
    {
        var mapping = new Dictionary<ushort, int>();

        foreach (var range in originalRanges)
        {
            var offset = range.StartAddress - optimizedStartAddress;
            mapping[range.StartAddress] = offset;
        }

        return mapping;
    }

    /// <summary>
    /// Extracts data for a specific range from optimized read result
    /// </summary>
    /// <param name="optimizedData">Data from optimized read</param>
    /// <param name="originalRange">Original range to extract data for</param>
    /// <param name="optimizedStartAddress">Start address of optimized read</param>
    /// <returns>Extracted data for the original range</returns>
    public static T[] ExtractRangeData<T>(
        T[] optimizedData,
        RegisterRange originalRange,
        ushort optimizedStartAddress)
    {
        var offset = originalRange.StartAddress - optimizedStartAddress;
        var result = new T[originalRange.Count];

        Array.Copy(optimizedData, offset, result, 0, originalRange.Count);

        return result;
    }
}
