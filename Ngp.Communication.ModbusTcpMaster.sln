﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.Communication.ModbusTcpMaster", "Ngp.Communication.ModbusTcpMaster\Ngp.Communication.ModbusTcpMaster.csproj", "{E2E7D42C-34EA-421D-9F23-60CE4AF3258D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.Communication.ModbusTcpMaster.Tests", "Ngp.Communication.ModbusTcpMaster.Tests\Ngp.Communication.ModbusTcpMaster.Tests.csproj", "{7F50395D-2644-4C47-A8DC-CDD958615FD9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.Communication.ModbusTcpMaster.WebApi", "Ngp.Communication.ModbusTcpMaster.WebApi\Ngp.Communication.ModbusTcpMaster.WebApi.csproj", "{3084003A-BCD0-48D9-8870-2D53A9246A2F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.Communication.ModbusTcpMaster.Simulator", "Ngp.Communication.ModbusTcpMaster.Simulator\Ngp.Communication.ModbusTcpMaster.Simulator.csproj", "{60D7E0F6-FDB0-4C8A-88BB-9C7741B18A91}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E2E7D42C-34EA-421D-9F23-60CE4AF3258D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E2E7D42C-34EA-421D-9F23-60CE4AF3258D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E2E7D42C-34EA-421D-9F23-60CE4AF3258D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{E2E7D42C-34EA-421D-9F23-60CE4AF3258D}.Debug|x64.Build.0 = Debug|Any CPU
		{E2E7D42C-34EA-421D-9F23-60CE4AF3258D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E2E7D42C-34EA-421D-9F23-60CE4AF3258D}.Debug|x86.Build.0 = Debug|Any CPU
		{E2E7D42C-34EA-421D-9F23-60CE4AF3258D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E2E7D42C-34EA-421D-9F23-60CE4AF3258D}.Release|Any CPU.Build.0 = Release|Any CPU
		{E2E7D42C-34EA-421D-9F23-60CE4AF3258D}.Release|x64.ActiveCfg = Release|Any CPU
		{E2E7D42C-34EA-421D-9F23-60CE4AF3258D}.Release|x64.Build.0 = Release|Any CPU
		{E2E7D42C-34EA-421D-9F23-60CE4AF3258D}.Release|x86.ActiveCfg = Release|Any CPU
		{E2E7D42C-34EA-421D-9F23-60CE4AF3258D}.Release|x86.Build.0 = Release|Any CPU
		{7F50395D-2644-4C47-A8DC-CDD958615FD9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7F50395D-2644-4C47-A8DC-CDD958615FD9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7F50395D-2644-4C47-A8DC-CDD958615FD9}.Debug|x64.ActiveCfg = Debug|Any CPU
		{7F50395D-2644-4C47-A8DC-CDD958615FD9}.Debug|x64.Build.0 = Debug|Any CPU
		{7F50395D-2644-4C47-A8DC-CDD958615FD9}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7F50395D-2644-4C47-A8DC-CDD958615FD9}.Debug|x86.Build.0 = Debug|Any CPU
		{7F50395D-2644-4C47-A8DC-CDD958615FD9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7F50395D-2644-4C47-A8DC-CDD958615FD9}.Release|Any CPU.Build.0 = Release|Any CPU
		{7F50395D-2644-4C47-A8DC-CDD958615FD9}.Release|x64.ActiveCfg = Release|Any CPU
		{7F50395D-2644-4C47-A8DC-CDD958615FD9}.Release|x64.Build.0 = Release|Any CPU
		{7F50395D-2644-4C47-A8DC-CDD958615FD9}.Release|x86.ActiveCfg = Release|Any CPU
		{7F50395D-2644-4C47-A8DC-CDD958615FD9}.Release|x86.Build.0 = Release|Any CPU
		{3084003A-BCD0-48D9-8870-2D53A9246A2F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3084003A-BCD0-48D9-8870-2D53A9246A2F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3084003A-BCD0-48D9-8870-2D53A9246A2F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{3084003A-BCD0-48D9-8870-2D53A9246A2F}.Debug|x64.Build.0 = Debug|Any CPU
		{3084003A-BCD0-48D9-8870-2D53A9246A2F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{3084003A-BCD0-48D9-8870-2D53A9246A2F}.Debug|x86.Build.0 = Debug|Any CPU
		{3084003A-BCD0-48D9-8870-2D53A9246A2F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3084003A-BCD0-48D9-8870-2D53A9246A2F}.Release|Any CPU.Build.0 = Release|Any CPU
		{3084003A-BCD0-48D9-8870-2D53A9246A2F}.Release|x64.ActiveCfg = Release|Any CPU
		{3084003A-BCD0-48D9-8870-2D53A9246A2F}.Release|x64.Build.0 = Release|Any CPU
		{3084003A-BCD0-48D9-8870-2D53A9246A2F}.Release|x86.ActiveCfg = Release|Any CPU
		{3084003A-BCD0-48D9-8870-2D53A9246A2F}.Release|x86.Build.0 = Release|Any CPU
		{60D7E0F6-FDB0-4C8A-88BB-9C7741B18A91}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{60D7E0F6-FDB0-4C8A-88BB-9C7741B18A91}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{60D7E0F6-FDB0-4C8A-88BB-9C7741B18A91}.Debug|x64.ActiveCfg = Debug|Any CPU
		{60D7E0F6-FDB0-4C8A-88BB-9C7741B18A91}.Debug|x64.Build.0 = Debug|Any CPU
		{60D7E0F6-FDB0-4C8A-88BB-9C7741B18A91}.Debug|x86.ActiveCfg = Debug|Any CPU
		{60D7E0F6-FDB0-4C8A-88BB-9C7741B18A91}.Debug|x86.Build.0 = Debug|Any CPU
		{60D7E0F6-FDB0-4C8A-88BB-9C7741B18A91}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{60D7E0F6-FDB0-4C8A-88BB-9C7741B18A91}.Release|Any CPU.Build.0 = Release|Any CPU
		{60D7E0F6-FDB0-4C8A-88BB-9C7741B18A91}.Release|x64.ActiveCfg = Release|Any CPU
		{60D7E0F6-FDB0-4C8A-88BB-9C7741B18A91}.Release|x64.Build.0 = Release|Any CPU
		{60D7E0F6-FDB0-4C8A-88BB-9C7741B18A91}.Release|x86.ActiveCfg = Release|Any CPU
		{60D7E0F6-FDB0-4C8A-88BB-9C7741B18A91}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
