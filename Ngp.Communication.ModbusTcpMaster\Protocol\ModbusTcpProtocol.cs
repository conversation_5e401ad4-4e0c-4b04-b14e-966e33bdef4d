using Ngp.Communication.ModbusTcpMaster.Models;

namespace Ngp.Communication.ModbusTcpMaster.Protocol;

/// <summary>
/// Implementation of Modbus TCP protocol
/// </summary>
public class ModbusTcpProtocol : ModbusProtocol
{
    private const int MbapHeaderLength = 6;
    private const ushort ProtocolIdentifier = 0x0000;

    /// <summary>
    /// Creates a read request message for Modbus TCP
    /// </summary>
    /// <param name="request">The read request</param>
    /// <param name="transactionId">Transaction ID for the request</param>
    /// <returns>The message bytes</returns>
    public override byte[] CreateReadRequest(ModbusReadRequest request, ushort transactionId)
    {
        var functionCode = GetFunctionCode(request.RegisterType);
        var pdu = CreateReadPdu(functionCode, request.StartAddress, request.Count);
        return CreateMbapFrame(transactionId, request.SlaveId, pdu);
    }

    /// <summary>
    /// Creates a single write request message for Modbus TCP
    /// </summary>
    /// <param name="request">The write request</param>
    /// <param name="transactionId">Transaction ID for the request</param>
    /// <returns>The message bytes</returns>
    public override byte[] CreateWriteSingleRequest<T>(ModbusWriteSingleRequest<T> request, ushort transactionId)
    {
        var functionCode = GetFunctionCode(request.RegisterType, isWrite: true, isMultiple: false);
        byte[] pdu;

        if (typeof(T) == typeof(bool))
        {
            var value = (bool)(object)request.Value!;
            pdu = CreateWriteSingleCoilPdu(functionCode, request.Address, value);
        }
        else if (typeof(T) == typeof(ushort))
        {
            var value = (ushort)(object)request.Value!;
            pdu = CreateWriteSingleRegisterPdu(functionCode, request.Address, value);
        }
        else
        {
            throw new ArgumentException($"Unsupported value type: {typeof(T)}");
        }

        return CreateMbapFrame(transactionId, request.SlaveId, pdu);
    }

    /// <summary>
    /// Creates a multiple write request message for Modbus TCP
    /// </summary>
    /// <param name="request">The write request</param>
    /// <param name="transactionId">Transaction ID for the request</param>
    /// <returns>The message bytes</returns>
    public override byte[] CreateWriteMultipleRequest<T>(ModbusWriteMultipleRequest<T> request, ushort transactionId)
    {
        var functionCode = GetFunctionCode(request.RegisterType, isWrite: true, isMultiple: true);
        byte[] pdu;

        if (typeof(T) == typeof(bool))
        {
            var values = (bool[])(object)request.Values;
            pdu = CreateWriteMultipleCoilsPdu(functionCode, request.StartAddress, values);
        }
        else if (typeof(T) == typeof(ushort))
        {
            var values = (ushort[])(object)request.Values;
            pdu = CreateWriteMultipleRegistersPdu(functionCode, request.StartAddress, values);
        }
        else
        {
            throw new ArgumentException($"Unsupported value type: {typeof(T)}");
        }

        return CreateMbapFrame(transactionId, request.SlaveId, pdu);
    }

    /// <summary>
    /// Parses a Modbus TCP response message
    /// </summary>
    /// <param name="response">The response bytes</param>
    /// <param name="expectedTransactionId">Expected transaction ID</param>
    /// <returns>Parsed response data</returns>
    public override ModbusResponseData ParseResponse(byte[] response, ushort expectedTransactionId)
    {
        if (response.Length < MbapHeaderLength + 2)
        {
            throw new InvalidOperationException("Response too short for Modbus TCP");
        }

        var transactionId = FromBigEndianBytes(response, 0);
        var protocolId = FromBigEndianBytes(response, 2);
        var length = FromBigEndianBytes(response, 4);
        var unitId = response[6];
        var functionCode = response[7];

        if (protocolId != ProtocolIdentifier)
        {
            throw new InvalidOperationException($"Invalid protocol identifier: {protocolId}");
        }

        if (transactionId != expectedTransactionId)
        {
            throw new InvalidOperationException($"Transaction ID mismatch. Expected: {expectedTransactionId}, Received: {transactionId}");
        }

        var isError = ModbusFunctionCodes.IsErrorResponse(functionCode);
        byte? exceptionCode = null;
        byte[]? data = null;

        if (isError)
        {
            if (response.Length >= MbapHeaderLength + 3)
            {
                exceptionCode = response[8];
            }
        }
        else
        {
            var dataLength = length - 2; // Subtract unit ID and function code
            if (dataLength > 0 && response.Length >= MbapHeaderLength + 2 + dataLength)
            {
                data = new byte[dataLength];
                Array.Copy(response, MbapHeaderLength + 2, data, 0, dataLength);
            }
        }

        return new ModbusResponseData
        {
            TransactionId = transactionId,
            UnitId = unitId,
            FunctionCode = functionCode,
            IsError = isError,
            ExceptionCode = exceptionCode,
            Data = data,
            RawResponse = response
        };
    }

    /// <summary>
    /// Validates if the Modbus TCP response is complete
    /// </summary>
    /// <param name="response">The response bytes</param>
    /// <returns>True if complete, false otherwise</returns>
    public override bool IsResponseComplete(byte[] response)
    {
        if (response.Length < MbapHeaderLength)
        {
            return false;
        }

        var length = FromBigEndianBytes(response, 4);
        var expectedTotalLength = MbapHeaderLength + length;

        return response.Length >= expectedTotalLength;
    }

    /// <summary>
    /// Gets the expected response length for a Modbus TCP read request
    /// </summary>
    /// <param name="request">The request</param>
    /// <returns>Expected response length in bytes</returns>
    public override int GetExpectedResponseLength(ModbusReadRequest request)
    {
        int dataLength = request.RegisterType switch
        {
            ModbusRegisterType.Coil or ModbusRegisterType.DiscreteInput => 1 + (request.Count + 7) / 8, // Byte count + data
            ModbusRegisterType.HoldingRegister or ModbusRegisterType.InputRegister => 1 + (request.Count * 2), // Byte count + data
            _ => throw new ArgumentException($"Invalid register type: {request.RegisterType}")
        };

        return MbapHeaderLength + 2 + dataLength; // MBAP header + unit ID + function code + data
    }

    /// <summary>
    /// Creates MBAP (Modbus Application Protocol) frame
    /// </summary>
    /// <param name="transactionId">Transaction identifier</param>
    /// <param name="unitId">Unit identifier (slave ID)</param>
    /// <param name="pdu">Protocol data unit</param>
    /// <returns>Complete MBAP frame</returns>
    private static byte[] CreateMbapFrame(ushort transactionId, byte unitId, byte[] pdu)
    {
        var length = (ushort)(pdu.Length + 1); // PDU length + unit ID
        var frame = new byte[MbapHeaderLength + pdu.Length];

        // Transaction ID
        var transactionBytes = ToBigEndianBytes(transactionId);
        frame[0] = transactionBytes[0];
        frame[1] = transactionBytes[1];

        // Protocol ID
        frame[2] = 0x00;
        frame[3] = 0x00;

        // Length
        var lengthBytes = ToBigEndianBytes(length);
        frame[4] = lengthBytes[0];
        frame[5] = lengthBytes[1];

        // Unit ID
        frame[6] = unitId;

        // PDU
        Array.Copy(pdu, 0, frame, 7, pdu.Length);

        return frame;
    }

    /// <summary>
    /// Creates PDU for read operations
    /// </summary>
    /// <param name="functionCode">Function code</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="count">Number of items to read</param>
    /// <returns>PDU bytes</returns>
    private static byte[] CreateReadPdu(byte functionCode, ushort startAddress, ushort count)
    {
        var pdu = new byte[5];
        pdu[0] = functionCode;

        var addressBytes = ToBigEndianBytes(startAddress);
        pdu[1] = addressBytes[0];
        pdu[2] = addressBytes[1];

        var countBytes = ToBigEndianBytes(count);
        pdu[3] = countBytes[0];
        pdu[4] = countBytes[1];

        return pdu;
    }

    /// <summary>
    /// Creates PDU for single coil write operation
    /// </summary>
    /// <param name="functionCode">Function code</param>
    /// <param name="address">Coil address</param>
    /// <param name="value">Coil value</param>
    /// <returns>PDU bytes</returns>
    private static byte[] CreateWriteSingleCoilPdu(byte functionCode, ushort address, bool value)
    {
        var pdu = new byte[5];
        pdu[0] = functionCode;

        var addressBytes = ToBigEndianBytes(address);
        pdu[1] = addressBytes[0];
        pdu[2] = addressBytes[1];

        // Coil value: 0xFF00 for ON, 0x0000 for OFF
        pdu[3] = value ? (byte)0xFF : (byte)0x00;
        pdu[4] = 0x00;

        return pdu;
    }

    /// <summary>
    /// Creates PDU for single register write operation
    /// </summary>
    /// <param name="functionCode">Function code</param>
    /// <param name="address">Register address</param>
    /// <param name="value">Register value</param>
    /// <returns>PDU bytes</returns>
    private static byte[] CreateWriteSingleRegisterPdu(byte functionCode, ushort address, ushort value)
    {
        var pdu = new byte[5];
        pdu[0] = functionCode;

        var addressBytes = ToBigEndianBytes(address);
        pdu[1] = addressBytes[0];
        pdu[2] = addressBytes[1];

        var valueBytes = ToBigEndianBytes(value);
        pdu[3] = valueBytes[0];
        pdu[4] = valueBytes[1];

        return pdu;
    }

    /// <summary>
    /// Creates PDU for multiple coils write operation
    /// </summary>
    /// <param name="functionCode">Function code</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="values">Coil values</param>
    /// <returns>PDU bytes</returns>
    private static byte[] CreateWriteMultipleCoilsPdu(byte functionCode, ushort startAddress, bool[] values)
    {
        var packedValues = PackBooleans(values);
        var pdu = new byte[6 + packedValues.Length];

        pdu[0] = functionCode;

        var addressBytes = ToBigEndianBytes(startAddress);
        pdu[1] = addressBytes[0];
        pdu[2] = addressBytes[1];

        var countBytes = ToBigEndianBytes((ushort)values.Length);
        pdu[3] = countBytes[0];
        pdu[4] = countBytes[1];

        pdu[5] = (byte)packedValues.Length;

        Array.Copy(packedValues, 0, pdu, 6, packedValues.Length);

        return pdu;
    }

    /// <summary>
    /// Creates PDU for multiple registers write operation
    /// </summary>
    /// <param name="functionCode">Function code</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="values">Register values</param>
    /// <returns>PDU bytes</returns>
    private static byte[] CreateWriteMultipleRegistersPdu(byte functionCode, ushort startAddress, ushort[] values)
    {
        var pdu = new byte[6 + (values.Length * 2)];

        pdu[0] = functionCode;

        var addressBytes = ToBigEndianBytes(startAddress);
        pdu[1] = addressBytes[0];
        pdu[2] = addressBytes[1];

        var countBytes = ToBigEndianBytes((ushort)values.Length);
        pdu[3] = countBytes[0];
        pdu[4] = countBytes[1];

        pdu[5] = (byte)(values.Length * 2);

        for (int i = 0; i < values.Length; i++)
        {
            var valueBytes = ToBigEndianBytes(values[i]);
            pdu[6 + (i * 2)] = valueBytes[0];
            pdu[7 + (i * 2)] = valueBytes[1];
        }

        return pdu;
    }
}
