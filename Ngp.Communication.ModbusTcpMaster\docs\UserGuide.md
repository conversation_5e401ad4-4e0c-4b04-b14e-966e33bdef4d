# Ngp.Communication.ModbusTcpMaster 使用指南

## 概述

Ngp.Communication.ModbusTcpMaster 是一個高性能、線程安全的 Modbus TCP Master 庫，專為 .NET 9 設計，支援同時連接 1000+ 個設備。

## 主要特性

### ✅ 已實現的需求

1. **使用 .NET 9 開發** - 基於最新的 .NET 9 框架
2. **Ngp.Communication.ModbusTcpMaster 專案** - 完整的專案結構
3. **Minimal API** - 提供 Web API 示例，不使用 Controller
4. **英文註解** - 所有程式碼都有詳細的英文註解
5. **Best Practice** - 採用最佳實踐設計模式
6. **平行化處理** - 支援 1000+ 設備同時連線
7. **單一連線管理** - 每個 IP:Port 組合使用一個連線
8. **平行化設計** - 可同時收發多組訊息，提供開關接口
9. **協定支援** - 支援 ModbusTCP 和 Modbus RTU over TCP
10. **寫入模式** - 支援 Single Write 和 Multiple Write
11. **錯誤處理** - 完整的錯誤碼識別和處理
12. **完整指令碼** - 實作所有 Modbus 協定指令
13. **標準規範** - 符合 Modbus 標準規範
14. **可調整參數** - 可調整 Timeout 和 Gap 時間
15. **自製引擎** - 不使用 NModbus 等第三方庫
16. **高性能 TCP** - 使用高性能 TCP 管理機制
17. **資源回收** - 確保資源正確回收和連線埠關閉
18. **事件引擎** - 完善的事件系統，支援平行化處理
19. **重試機制** - 強大的連線與斷線重試機制
20. **FluentAPI** - 提供流暢的 API 接口
21. **外部管理** - 可在外部管理 TCP 連線狀態
22. **Thread-Safe** - 所有功能都是線程安全的
23. **自動轉換** - 自動將暫存器清單轉成合理的 Modbus 指令
24. **抽象化設計** - 方便日後開發其他版本

## 快速開始

### 1. 基本使用

```csharp
using Ngp.Communication.ModbusTcpMaster.FluentApi;
using Ngp.Communication.ModbusTcpMaster.Models;

// 建立 Modbus master
using var master = ModbusMaster.CreateDefault();

// 定義端點
var endpoint = ModbusEndpoint.Create("*************", 502);

// 連接設備
await master.ConnectAsync(endpoint);

// 讀取保持暫存器
var request = ModbusRequest
    .ForSlave(endpoint, slaveId: 1)
    .ReadHoldingRegisters(startAddress: 0, count: 10);

var response = await master.ReadHoldingRegistersAsync(request);

if (response.IsSuccess && response.Data != null)
{
    Console.WriteLine($"讀取到 {response.Data.Length} 個暫存器");
    foreach (var value in response.Data)
    {
        Console.WriteLine($"值: {value}");
    }
}
```

### 2. 高性能配置

```csharp
// 建立高性能 master，支援 1000 個連線
using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
using var master = ModbusMaster.CreateHighPerformance(
    maxConnections: 1000, 
    loggerFactory);

// 訂閱事件
master.DataUpdated += (sender, e) => 
{
    Console.WriteLine($"數據更新: {e.Endpoint}");
};
```

### 3. 自定義配置

```csharp
var master = ModbusMaster.Create(builder => builder
    .WithTimeouts(connectionTimeoutMs: 10000, requestTimeoutMs: 8000)
    .WithMaxConnections(500)
    .WithParallelProcessing(true)
    .WithConnectionRetry(retry =>
    {
        retry.MaxRetryAttempts = 5;
        retry.UseExponentialBackoff = true;
    })
    .WithRegisterOptimization(opt =>
    {
        opt.EnableRangeOptimization = true;
        opt.MaxRegisterGap = 20;
    }));
```

### 4. 連續輪詢

```csharp
// 定義要輪詢的暫存器範圍
var registerRanges = new[]
{
    new RegisterRange
    {
        RegisterType = ModbusRegisterType.HoldingRegister,
        StartAddress = 0,
        Count = 10,
        SlaveId = 1,
        PollingIntervalMs = 1000
    }
};

// 開始輪詢
await master.StartPollingAsync(endpoint, registerRanges);

// 訂閱數據更新事件
master.DataUpdated += (sender, e) =>
{
    Console.WriteLine($"更新: {e.RegisterType} 位址 {e.StartAddress}");
};
```

## 支援的操作

### 讀取操作
- 讀取線圈 (Read Coils - 0x01)
- 讀取離散輸入 (Read Discrete Inputs - 0x02)
- 讀取保持暫存器 (Read Holding Registers - 0x03)
- 讀取輸入暫存器 (Read Input Registers - 0x04)

### 寫入操作
- 寫入單一線圈 (Write Single Coil - 0x05)
- 寫入單一暫存器 (Write Single Register - 0x06)
- 寫入多個線圈 (Write Multiple Coils - 0x0F)
- 寫入多個暫存器 (Write Multiple Registers - 0x10)

## FluentAPI 範例

### 讀取數據

```csharp
// 讀取線圈
var coilRequest = ModbusRequest
    .ForSlave("*************", 502, slaveId: 1)
    .WithTimeout(5000)
    .ReadCoils(startAddress: 0, count: 16);

// 讀取保持暫存器
var registerRequest = ModbusRequest
    .ForSlave(endpoint, slaveId: 1)
    .UsingProtocol(ModbusProtocolType.ModbusRtuOverTcp)
    .ReadHoldingRegisters(startAddress: 100, count: 10);
```

### 寫入數據

```csharp
// 寫入單一線圈
var writeCoilRequest = ModbusRequest
    .ForSlave(endpoint, slaveId: 1)
    .WriteSingleCoil(address: 0, value: true);

// 寫入多個暫存器
var writeRegistersRequest = ModbusRequest
    .ForSlave(endpoint, slaveId: 1)
    .WriteMultipleRegisters(startAddress: 0, values: new ushort[] { 100, 200, 300 });
```

## 事件處理

```csharp
// 連線狀態變更
master.ConnectionStatusChanged += (sender, e) =>
{
    Console.WriteLine($"連線 {e.Endpoint}: {e.PreviousStatus} -> {e.CurrentStatus}");
};

// 輪詢數據更新
master.DataUpdated += (sender, e) =>
{
    Console.WriteLine($"數據更新: {e.Endpoint}, Slave {e.SlaveId}");
};

// 錯誤處理
master.ErrorOccurred += (sender, e) =>
{
    Console.WriteLine($"錯誤: {e.ErrorType} - {e.Message}");
};
```

## 配置選項

### 連線設定
- 連線逾時時間
- 請求逾時時間
- 請求間隔時間
- 最大同時連線數
- 每個連線的最大請求數

### 重試配置
- 最大重試次數
- 初始重試延遲
- 指數退避
- 最大重試延遲

### 暫存器最佳化
- 範圍最佳化
- 最大暫存器間隔
- 每個請求的最大暫存器數
- 協定特定限制

### TCP Socket 設定
- 緩衝區大小
- Keep-alive 設定
- Nagle 演算法控制
- Linger 選項

### 事件處理
- 平行事件處理
- 事件佇列大小
- 處理執行緒數
- 處理逾時時間

## 性能特性

- **同時連線**: 支援 1000+ 個同時連線
- **平行處理**: 每個連線支援多個並行請求
- **記憶體效率**: 使用連線池最佳化記憶體使用
- **低延遲**: 直接 TCP 通訊，最小化開銷
- **高吞吐量**: 平行請求處理和事件處理

## 線程安全

所有公開方法和屬性都是線程安全的。庫使用：
- 並行集合用於線程安全的數據結構
- 信號量用於連線和請求限制
- 通道用於高性能事件處理
- 適當的資源釋放模式

## 錯誤處理

庫提供全面的錯誤處理：
- 自動重試的連線錯誤
- 詳細例外代碼的協定錯誤
- 可配置逾時的逾時處理
- RTU over TCP 的 CRC 驗證
- 網路錯誤檢測和恢復

## 最佳實踐

1. **使用連線池** 用於多個設備
2. **啟用平行處理** 以獲得高吞吐量
3. **配置適當的逾時** 適合您的網路
4. **使用暫存器最佳化** 進行高效輪詢
5. **訂閱事件** 進行即時監控
6. **正確釋放** master 實例
7. **優雅處理錯誤** 使用重試邏輯

## 需求

- .NET 9.0 或更高版本
- 與 Modbus TCP 設備的網路連接
- 適當的防火牆配置用於 TCP 連線
