
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36127.28
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp", "Ngp\Ngp.csproj", "{599CD24F-3772-5C08-2C37-A924BF0F9054}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.AppHost", "Ngp.AppHost\Ngp.AppHost.csproj", "{64880E8A-522D-4348-BC1E-9419F481D1BC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.ServiceDefaults", "Ngp.ServiceDefaults\Ngp.ServiceDefaults.csproj", "{3A4875ED-8733-FE16-567D-5A3BA8EC050E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.Communication.ModbusTcpMaster", "Ngp.Communication.ModbusTcpMaster\Ngp.Communication.ModbusTcpMaster.csproj", "{E029160C-8382-48AD-86BA-84467B7A9844}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.Communication.ModbusTcpMaster.Tests", "Ngp.Communication.ModbusTcpMaster.Tests\Ngp.Communication.ModbusTcpMaster.Tests.csproj", "{C78000CE-5B52-41EF-9F29-B1EB57FF8DA3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.Communication.ModbusTcpMaster.WebApi", "Ngp.Communication.ModbusTcpMaster.WebApi\Ngp.Communication.ModbusTcpMaster.WebApi.csproj", "{F9D42A4B-646D-4432-BB63-F3E5BBA33F91}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ngp.Communication.ModbusTcpMaster.Simulator", "Ngp.Communication.ModbusTcpMaster.Simulator\Ngp.Communication.ModbusTcpMaster.Simulator.csproj", "{ACBE4A3A-9787-42B6-96B6-2A0198BE9BDC}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{599CD24F-3772-5C08-2C37-A924BF0F9054}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{599CD24F-3772-5C08-2C37-A924BF0F9054}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{599CD24F-3772-5C08-2C37-A924BF0F9054}.Debug|x64.ActiveCfg = Debug|Any CPU
		{599CD24F-3772-5C08-2C37-A924BF0F9054}.Debug|x64.Build.0 = Debug|Any CPU
		{599CD24F-3772-5C08-2C37-A924BF0F9054}.Debug|x86.ActiveCfg = Debug|Any CPU
		{599CD24F-3772-5C08-2C37-A924BF0F9054}.Debug|x86.Build.0 = Debug|Any CPU
		{599CD24F-3772-5C08-2C37-A924BF0F9054}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{599CD24F-3772-5C08-2C37-A924BF0F9054}.Release|Any CPU.Build.0 = Release|Any CPU
		{599CD24F-3772-5C08-2C37-A924BF0F9054}.Release|x64.ActiveCfg = Release|Any CPU
		{599CD24F-3772-5C08-2C37-A924BF0F9054}.Release|x64.Build.0 = Release|Any CPU
		{599CD24F-3772-5C08-2C37-A924BF0F9054}.Release|x86.ActiveCfg = Release|Any CPU
		{599CD24F-3772-5C08-2C37-A924BF0F9054}.Release|x86.Build.0 = Release|Any CPU
		{64880E8A-522D-4348-BC1E-9419F481D1BC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{64880E8A-522D-4348-BC1E-9419F481D1BC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{64880E8A-522D-4348-BC1E-9419F481D1BC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{64880E8A-522D-4348-BC1E-9419F481D1BC}.Debug|x64.Build.0 = Debug|Any CPU
		{64880E8A-522D-4348-BC1E-9419F481D1BC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{64880E8A-522D-4348-BC1E-9419F481D1BC}.Debug|x86.Build.0 = Debug|Any CPU
		{64880E8A-522D-4348-BC1E-9419F481D1BC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{64880E8A-522D-4348-BC1E-9419F481D1BC}.Release|Any CPU.Build.0 = Release|Any CPU
		{64880E8A-522D-4348-BC1E-9419F481D1BC}.Release|x64.ActiveCfg = Release|Any CPU
		{64880E8A-522D-4348-BC1E-9419F481D1BC}.Release|x64.Build.0 = Release|Any CPU
		{64880E8A-522D-4348-BC1E-9419F481D1BC}.Release|x86.ActiveCfg = Release|Any CPU
		{64880E8A-522D-4348-BC1E-9419F481D1BC}.Release|x86.Build.0 = Release|Any CPU
		{3A4875ED-8733-FE16-567D-5A3BA8EC050E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3A4875ED-8733-FE16-567D-5A3BA8EC050E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3A4875ED-8733-FE16-567D-5A3BA8EC050E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{3A4875ED-8733-FE16-567D-5A3BA8EC050E}.Debug|x64.Build.0 = Debug|Any CPU
		{3A4875ED-8733-FE16-567D-5A3BA8EC050E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{3A4875ED-8733-FE16-567D-5A3BA8EC050E}.Debug|x86.Build.0 = Debug|Any CPU
		{3A4875ED-8733-FE16-567D-5A3BA8EC050E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3A4875ED-8733-FE16-567D-5A3BA8EC050E}.Release|Any CPU.Build.0 = Release|Any CPU
		{3A4875ED-8733-FE16-567D-5A3BA8EC050E}.Release|x64.ActiveCfg = Release|Any CPU
		{3A4875ED-8733-FE16-567D-5A3BA8EC050E}.Release|x64.Build.0 = Release|Any CPU
		{3A4875ED-8733-FE16-567D-5A3BA8EC050E}.Release|x86.ActiveCfg = Release|Any CPU
		{3A4875ED-8733-FE16-567D-5A3BA8EC050E}.Release|x86.Build.0 = Release|Any CPU
		{E029160C-8382-48AD-86BA-84467B7A9844}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E029160C-8382-48AD-86BA-84467B7A9844}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E029160C-8382-48AD-86BA-84467B7A9844}.Debug|x64.ActiveCfg = Debug|Any CPU
		{E029160C-8382-48AD-86BA-84467B7A9844}.Debug|x64.Build.0 = Debug|Any CPU
		{E029160C-8382-48AD-86BA-84467B7A9844}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E029160C-8382-48AD-86BA-84467B7A9844}.Debug|x86.Build.0 = Debug|Any CPU
		{E029160C-8382-48AD-86BA-84467B7A9844}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E029160C-8382-48AD-86BA-84467B7A9844}.Release|Any CPU.Build.0 = Release|Any CPU
		{E029160C-8382-48AD-86BA-84467B7A9844}.Release|x64.ActiveCfg = Release|Any CPU
		{E029160C-8382-48AD-86BA-84467B7A9844}.Release|x64.Build.0 = Release|Any CPU
		{E029160C-8382-48AD-86BA-84467B7A9844}.Release|x86.ActiveCfg = Release|Any CPU
		{E029160C-8382-48AD-86BA-84467B7A9844}.Release|x86.Build.0 = Release|Any CPU
		{C78000CE-5B52-41EF-9F29-B1EB57FF8DA3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C78000CE-5B52-41EF-9F29-B1EB57FF8DA3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C78000CE-5B52-41EF-9F29-B1EB57FF8DA3}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C78000CE-5B52-41EF-9F29-B1EB57FF8DA3}.Debug|x64.Build.0 = Debug|Any CPU
		{C78000CE-5B52-41EF-9F29-B1EB57FF8DA3}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C78000CE-5B52-41EF-9F29-B1EB57FF8DA3}.Debug|x86.Build.0 = Debug|Any CPU
		{C78000CE-5B52-41EF-9F29-B1EB57FF8DA3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C78000CE-5B52-41EF-9F29-B1EB57FF8DA3}.Release|Any CPU.Build.0 = Release|Any CPU
		{C78000CE-5B52-41EF-9F29-B1EB57FF8DA3}.Release|x64.ActiveCfg = Release|Any CPU
		{C78000CE-5B52-41EF-9F29-B1EB57FF8DA3}.Release|x64.Build.0 = Release|Any CPU
		{C78000CE-5B52-41EF-9F29-B1EB57FF8DA3}.Release|x86.ActiveCfg = Release|Any CPU
		{C78000CE-5B52-41EF-9F29-B1EB57FF8DA3}.Release|x86.Build.0 = Release|Any CPU
		{F9D42A4B-646D-4432-BB63-F3E5BBA33F91}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F9D42A4B-646D-4432-BB63-F3E5BBA33F91}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F9D42A4B-646D-4432-BB63-F3E5BBA33F91}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F9D42A4B-646D-4432-BB63-F3E5BBA33F91}.Debug|x64.Build.0 = Debug|Any CPU
		{F9D42A4B-646D-4432-BB63-F3E5BBA33F91}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F9D42A4B-646D-4432-BB63-F3E5BBA33F91}.Debug|x86.Build.0 = Debug|Any CPU
		{F9D42A4B-646D-4432-BB63-F3E5BBA33F91}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F9D42A4B-646D-4432-BB63-F3E5BBA33F91}.Release|Any CPU.Build.0 = Release|Any CPU
		{F9D42A4B-646D-4432-BB63-F3E5BBA33F91}.Release|x64.ActiveCfg = Release|Any CPU
		{F9D42A4B-646D-4432-BB63-F3E5BBA33F91}.Release|x64.Build.0 = Release|Any CPU
		{F9D42A4B-646D-4432-BB63-F3E5BBA33F91}.Release|x86.ActiveCfg = Release|Any CPU
		{F9D42A4B-646D-4432-BB63-F3E5BBA33F91}.Release|x86.Build.0 = Release|Any CPU
		{ACBE4A3A-9787-42B6-96B6-2A0198BE9BDC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ACBE4A3A-9787-42B6-96B6-2A0198BE9BDC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ACBE4A3A-9787-42B6-96B6-2A0198BE9BDC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{ACBE4A3A-9787-42B6-96B6-2A0198BE9BDC}.Debug|x64.Build.0 = Debug|Any CPU
		{ACBE4A3A-9787-42B6-96B6-2A0198BE9BDC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{ACBE4A3A-9787-42B6-96B6-2A0198BE9BDC}.Debug|x86.Build.0 = Debug|Any CPU
		{ACBE4A3A-9787-42B6-96B6-2A0198BE9BDC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ACBE4A3A-9787-42B6-96B6-2A0198BE9BDC}.Release|Any CPU.Build.0 = Release|Any CPU
		{ACBE4A3A-9787-42B6-96B6-2A0198BE9BDC}.Release|x64.ActiveCfg = Release|Any CPU
		{ACBE4A3A-9787-42B6-96B6-2A0198BE9BDC}.Release|x64.Build.0 = Release|Any CPU
		{ACBE4A3A-9787-42B6-96B6-2A0198BE9BDC}.Release|x86.ActiveCfg = Release|Any CPU
		{ACBE4A3A-9787-42B6-96B6-2A0198BE9BDC}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {3AD24F1F-38EC-49FA-8DE5-131EBDDE92E2}
	EndGlobalSection
EndGlobal
