using Ngp.Communication.ModbusTcpMaster.Models;

namespace Ngp.Communication.ModbusTcpMaster.Events;

/// <summary>
/// Event arguments for connection status changes
/// </summary>
public class ConnectionStatusEventArgs : EventArgs
{
    /// <summary>
    /// The endpoint whose status changed
    /// </summary>
    public required ModbusEndpoint Endpoint { get; init; }

    /// <summary>
    /// The previous connection status
    /// </summary>
    public required ConnectionStatus PreviousStatus { get; init; }

    /// <summary>
    /// The current connection status
    /// </summary>
    public required ConnectionStatus CurrentStatus { get; init; }

    /// <summary>
    /// The timestamp when the status changed
    /// </summary>
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;

    /// <summary>
    /// Additional information about the status change
    /// </summary>
    public string? Message { get; init; }
}

/// <summary>
/// Event arguments for data updates
/// </summary>
public class DataUpdatedEventArgs : EventArgs
{
    /// <summary>
    /// The endpoint that provided the data
    /// </summary>
    public required ModbusEndpoint Endpoint { get; init; }

    /// <summary>
    /// The slave ID that provided the data
    /// </summary>
    public required byte SlaveId { get; init; }

    /// <summary>
    /// The type of register that was updated
    /// </summary>
    public required ModbusRegisterType RegisterType { get; init; }

    /// <summary>
    /// The starting address of the updated data
    /// </summary>
    public required ushort StartAddress { get; init; }

    /// <summary>
    /// The updated data
    /// </summary>
    public required object Data { get; init; }

    /// <summary>
    /// The timestamp when the data was updated
    /// </summary>
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Event arguments for Modbus errors
/// </summary>
public class ModbusErrorEventArgs : EventArgs
{
    /// <summary>
    /// The endpoint where the error occurred
    /// </summary>
    public required ModbusEndpoint Endpoint { get; init; }

    /// <summary>
    /// The slave ID where the error occurred (if applicable)
    /// </summary>
    public byte? SlaveId { get; init; }

    /// <summary>
    /// The type of error
    /// </summary>
    public required ModbusErrorType ErrorType { get; init; }

    /// <summary>
    /// The error message
    /// </summary>
    public required string Message { get; init; }

    /// <summary>
    /// The exception that caused the error (if applicable)
    /// </summary>
    public Exception? Exception { get; init; }

    /// <summary>
    /// Modbus exception code (if applicable)
    /// </summary>
    public byte? ExceptionCode { get; init; }

    /// <summary>
    /// The timestamp when the error occurred
    /// </summary>
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// Types of Modbus errors
/// </summary>
public enum ModbusErrorType
{
    /// <summary>
    /// Connection error
    /// </summary>
    ConnectionError,

    /// <summary>
    /// Timeout error
    /// </summary>
    TimeoutError,

    /// <summary>
    /// CRC error
    /// </summary>
    CrcError,

    /// <summary>
    /// Protocol error
    /// </summary>
    ProtocolError,

    /// <summary>
    /// Modbus exception response
    /// </summary>
    ModbusException,

    /// <summary>
    /// Network error
    /// </summary>
    NetworkError,

    /// <summary>
    /// Unknown error
    /// </summary>
    UnknownError
}
