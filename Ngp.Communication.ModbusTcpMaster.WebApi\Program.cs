using Ngp.Communication.ModbusTcpMaster.Abstractions;
using Ngp.Communication.ModbusTcpMaster.FluentApi;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Events;
using System.Collections.Concurrent;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Configure logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.SetMinimumLevel(LogLevel.Information);

// Register ModbusMaster as singleton
builder.Services.AddSingleton<IModbusMaster>(serviceProvider =>
{
    var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
    var master = ModbusMaster.CreateHighPerformance(maxConnections: 1000, loggerFactory);
    
    // Subscribe to events for logging
    master.ConnectionStatusChanged += (sender, e) =>
    {
        var logger = loggerFactory.CreateLogger("ModbusMaster");
        logger.LogInformation("Connection status changed for {Endpoint}: {PreviousStatus} -> {CurrentStatus}",
            e.Endpoint, e.PreviousStatus, e.CurrentStatus);
    };
    
    master.ErrorOccurred += (sender, e) =>
    {
        var logger = loggerFactory.CreateLogger("ModbusMaster");
        logger.LogError("Modbus error for {Endpoint}: {ErrorType} - {Message}",
            e.Endpoint, e.ErrorType, e.Message);
    };
    
    return master;
});

// Store for active polling sessions
builder.Services.AddSingleton<ConcurrentDictionary<string, bool>>();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

// Modbus TCP Master API endpoints

/// <summary>
/// Connect to a Modbus device
/// </summary>
app.MapPost("/modbus/connect", async (
    string ipAddress,
    int port,
    IModbusMaster modbusMaster) =>
{
    try
    {
        var endpoint = ModbusEndpoint.Create(ipAddress, port);
        var connected = await modbusMaster.ConnectAsync(endpoint);
        
        return connected 
            ? Results.Ok(new { success = true, message = $"Connected to {endpoint}" })
            : Results.BadRequest(new { success = false, message = $"Failed to connect to {endpoint}" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, message = ex.Message });
    }
})
.WithName("ConnectToDevice")
.WithOpenApi();

/// <summary>
/// Disconnect from a Modbus device
/// </summary>
app.MapPost("/modbus/disconnect", async (
    string ipAddress,
    int port,
    IModbusMaster modbusMaster) =>
{
    try
    {
        var endpoint = ModbusEndpoint.Create(ipAddress, port);
        await modbusMaster.DisconnectAsync(endpoint);
        
        return Results.Ok(new { success = true, message = $"Disconnected from {endpoint}" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, message = ex.Message });
    }
})
.WithName("DisconnectFromDevice")
.WithOpenApi();

/// <summary>
/// Get connection status for a device
/// </summary>
app.MapGet("/modbus/status", (
    string ipAddress,
    int port,
    IModbusMaster modbusMaster) =>
{
    try
    {
        var endpoint = ModbusEndpoint.Create(ipAddress, port);
        var status = modbusMaster.GetConnectionStatus(endpoint);
        
        return Results.Ok(new { endpoint = endpoint.ToString(), status = status.ToString() });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, message = ex.Message });
    }
})
.WithName("GetConnectionStatus")
.WithOpenApi();

/// <summary>
/// Read coils from a Modbus device
/// </summary>
app.MapGet("/modbus/read/coils", async (
    string ipAddress,
    int port,
    byte slaveId,
    ushort startAddress,
    ushort count,
    IModbusMaster modbusMaster) =>
{
    try
    {
        var endpoint = ModbusEndpoint.Create(ipAddress, port);
        var request = ModbusRequest
            .ForSlave(endpoint, slaveId)
            .ReadCoils(startAddress, count);
            
        var response = await modbusMaster.ReadCoilsAsync(request);
        
        return response.IsSuccess
            ? Results.Ok(new { 
                success = true, 
                data = response.Data, 
                roundTripTime = response.RoundTripTime.TotalMilliseconds 
              })
            : Results.BadRequest(new { 
                success = false, 
                message = response.ErrorMessage,
                exceptionCode = response.ExceptionCode 
              });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, message = ex.Message });
    }
})
.WithName("ReadCoils")
.WithOpenApi();

/// <summary>
/// Read holding registers from a Modbus device
/// </summary>
app.MapGet("/modbus/read/holding-registers", async (
    string ipAddress,
    int port,
    byte slaveId,
    ushort startAddress,
    ushort count,
    IModbusMaster modbusMaster) =>
{
    try
    {
        var endpoint = ModbusEndpoint.Create(ipAddress, port);
        var request = ModbusRequest
            .ForSlave(endpoint, slaveId)
            .ReadHoldingRegisters(startAddress, count);
            
        var response = await modbusMaster.ReadHoldingRegistersAsync(request);
        
        return response.IsSuccess
            ? Results.Ok(new { 
                success = true, 
                data = response.Data, 
                roundTripTime = response.RoundTripTime.TotalMilliseconds 
              })
            : Results.BadRequest(new { 
                success = false, 
                message = response.ErrorMessage,
                exceptionCode = response.ExceptionCode 
              });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, message = ex.Message });
    }
})
.WithName("ReadHoldingRegisters")
.WithOpenApi();

/// <summary>
/// Read input registers from a Modbus device
/// </summary>
app.MapGet("/modbus/read/input-registers", async (
    string ipAddress,
    int port,
    byte slaveId,
    ushort startAddress,
    ushort count,
    IModbusMaster modbusMaster) =>
{
    try
    {
        var endpoint = ModbusEndpoint.Create(ipAddress, port);
        var request = ModbusRequest
            .ForSlave(endpoint, slaveId)
            .ReadInputRegisters(startAddress, count);
            
        var response = await modbusMaster.ReadInputRegistersAsync(request);
        
        return response.IsSuccess
            ? Results.Ok(new { 
                success = true, 
                data = response.Data, 
                roundTripTime = response.RoundTripTime.TotalMilliseconds 
              })
            : Results.BadRequest(new { 
                success = false, 
                message = response.ErrorMessage,
                exceptionCode = response.ExceptionCode 
              });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, message = ex.Message });
    }
})
.WithName("ReadInputRegisters")
.WithOpenApi();

/// <summary>
/// Write a single coil to a Modbus device
/// </summary>
app.MapPost("/modbus/write/single-coil", async (
    string ipAddress,
    int port,
    byte slaveId,
    ushort address,
    bool value,
    IModbusMaster modbusMaster) =>
{
    try
    {
        var endpoint = ModbusEndpoint.Create(ipAddress, port);
        var request = ModbusRequest
            .ForSlave(endpoint, slaveId)
            .WriteSingleCoil(address, value);
            
        var response = await modbusMaster.WriteSingleCoilAsync(request);
        
        return response.IsSuccess
            ? Results.Ok(new { 
                success = true, 
                roundTripTime = response.RoundTripTime.TotalMilliseconds 
              })
            : Results.BadRequest(new { 
                success = false, 
                message = response.ErrorMessage,
                exceptionCode = response.ExceptionCode 
              });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, message = ex.Message });
    }
})
.WithName("WriteSingleCoil")
.WithOpenApi();

/// <summary>
/// Write a single holding register to a Modbus device
/// </summary>
app.MapPost("/modbus/write/single-register", async (
    string ipAddress,
    int port,
    byte slaveId,
    ushort address,
    ushort value,
    IModbusMaster modbusMaster) =>
{
    try
    {
        var endpoint = ModbusEndpoint.Create(ipAddress, port);
        var request = ModbusRequest
            .ForSlave(endpoint, slaveId)
            .WriteSingleRegister(address, value);
            
        var response = await modbusMaster.WriteSingleRegisterAsync(request);
        
        return response.IsSuccess
            ? Results.Ok(new { 
                success = true, 
                roundTripTime = response.RoundTripTime.TotalMilliseconds 
              })
            : Results.BadRequest(new { 
                success = false, 
                message = response.ErrorMessage,
                exceptionCode = response.ExceptionCode 
              });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { success = false, message = ex.Message });
    }
})
.WithName("WriteSingleRegister")
.WithOpenApi();

app.Run();
