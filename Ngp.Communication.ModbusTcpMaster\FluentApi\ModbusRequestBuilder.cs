using Ngp.Communication.ModbusTcpMaster.Models;

namespace Ngp.Communication.ModbusTcpMaster.FluentApi;

/// <summary>
/// Fluent API builder for creating Modbus requests
/// </summary>
public class ModbusRequestBuilder
{
    private ModbusEndpoint? _endpoint;
    private byte _slaveId;
    private int _timeoutMs = 5000;
    private ModbusProtocolType _protocolType = ModbusProtocolType.ModbusTcp;

    /// <summary>
    /// Sets the endpoint for the request
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <returns>Builder instance for chaining</returns>
    public ModbusRequestBuilder ToEndpoint(string ipAddress, int port)
    {
        _endpoint = ModbusEndpoint.Create(ipAddress, port);
        return this;
    }

    /// <summary>
    /// Sets the endpoint for the request
    /// </summary>
    /// <param name="endpoint">Modbus endpoint</param>
    /// <returns>Builder instance for chaining</returns>
    public ModbusRequestBuilder ToEndpoint(ModbusEndpoint endpoint)
    {
        _endpoint = endpoint ?? throw new ArgumentNullException(nameof(endpoint));
        return this;
    }

    /// <summary>
    /// Sets the slave ID for the request
    /// </summary>
    /// <param name="slaveId">Slave ID</param>
    /// <returns>Builder instance for chaining</returns>
    public ModbusRequestBuilder ForSlave(byte slaveId)
    {
        _slaveId = slaveId;
        return this;
    }

    /// <summary>
    /// Sets the timeout for the request
    /// </summary>
    /// <param name="timeoutMs">Timeout in milliseconds</param>
    /// <returns>Builder instance for chaining</returns>
    public ModbusRequestBuilder WithTimeout(int timeoutMs)
    {
        _timeoutMs = timeoutMs;
        return this;
    }

    /// <summary>
    /// Sets the protocol type for the request
    /// </summary>
    /// <param name="protocolType">Protocol type</param>
    /// <returns>Builder instance for chaining</returns>
    public ModbusRequestBuilder UsingProtocol(ModbusProtocolType protocolType)
    {
        _protocolType = protocolType;
        return this;
    }

    /// <summary>
    /// Creates a read coils request
    /// </summary>
    /// <param name="startAddress">Starting address</param>
    /// <param name="count">Number of coils to read</param>
    /// <returns>Read request</returns>
    public ModbusReadRequest ReadCoils(ushort startAddress, ushort count)
    {
        ValidateBaseParameters();
        return new ModbusReadRequest
        {
            Endpoint = _endpoint!,
            SlaveId = _slaveId,
            StartAddress = startAddress,
            Count = count,
            RegisterType = ModbusRegisterType.Coil,
            TimeoutMs = _timeoutMs,
            ProtocolType = _protocolType
        };
    }

    /// <summary>
    /// Creates a read discrete inputs request
    /// </summary>
    /// <param name="startAddress">Starting address</param>
    /// <param name="count">Number of discrete inputs to read</param>
    /// <returns>Read request</returns>
    public ModbusReadRequest ReadDiscreteInputs(ushort startAddress, ushort count)
    {
        ValidateBaseParameters();
        return new ModbusReadRequest
        {
            Endpoint = _endpoint!,
            SlaveId = _slaveId,
            StartAddress = startAddress,
            Count = count,
            RegisterType = ModbusRegisterType.DiscreteInput,
            TimeoutMs = _timeoutMs,
            ProtocolType = _protocolType
        };
    }

    /// <summary>
    /// Creates a read holding registers request
    /// </summary>
    /// <param name="startAddress">Starting address</param>
    /// <param name="count">Number of holding registers to read</param>
    /// <returns>Read request</returns>
    public ModbusReadRequest ReadHoldingRegisters(ushort startAddress, ushort count)
    {
        ValidateBaseParameters();
        return new ModbusReadRequest
        {
            Endpoint = _endpoint!,
            SlaveId = _slaveId,
            StartAddress = startAddress,
            Count = count,
            RegisterType = ModbusRegisterType.HoldingRegister,
            TimeoutMs = _timeoutMs,
            ProtocolType = _protocolType
        };
    }

    /// <summary>
    /// Creates a read input registers request
    /// </summary>
    /// <param name="startAddress">Starting address</param>
    /// <param name="count">Number of input registers to read</param>
    /// <returns>Read request</returns>
    public ModbusReadRequest ReadInputRegisters(ushort startAddress, ushort count)
    {
        ValidateBaseParameters();
        return new ModbusReadRequest
        {
            Endpoint = _endpoint!,
            SlaveId = _slaveId,
            StartAddress = startAddress,
            Count = count,
            RegisterType = ModbusRegisterType.InputRegister,
            TimeoutMs = _timeoutMs,
            ProtocolType = _protocolType
        };
    }

    /// <summary>
    /// Creates a write single coil request
    /// </summary>
    /// <param name="address">Coil address</param>
    /// <param name="value">Coil value</param>
    /// <returns>Write request</returns>
    public ModbusWriteSingleRequest<bool> WriteSingleCoil(ushort address, bool value)
    {
        ValidateBaseParameters();
        return new ModbusWriteSingleRequest<bool>
        {
            Endpoint = _endpoint!,
            SlaveId = _slaveId,
            Address = address,
            Value = value,
            RegisterType = ModbusRegisterType.Coil,
            TimeoutMs = _timeoutMs,
            ProtocolType = _protocolType
        };
    }

    /// <summary>
    /// Creates a write single holding register request
    /// </summary>
    /// <param name="address">Register address</param>
    /// <param name="value">Register value</param>
    /// <returns>Write request</returns>
    public ModbusWriteSingleRequest<ushort> WriteSingleRegister(ushort address, ushort value)
    {
        ValidateBaseParameters();
        return new ModbusWriteSingleRequest<ushort>
        {
            Endpoint = _endpoint!,
            SlaveId = _slaveId,
            Address = address,
            Value = value,
            RegisterType = ModbusRegisterType.HoldingRegister,
            TimeoutMs = _timeoutMs,
            ProtocolType = _protocolType
        };
    }

    /// <summary>
    /// Creates a write multiple coils request
    /// </summary>
    /// <param name="startAddress">Starting address</param>
    /// <param name="values">Coil values</param>
    /// <returns>Write request</returns>
    public ModbusWriteMultipleRequest<bool> WriteMultipleCoils(ushort startAddress, bool[] values)
    {
        ValidateBaseParameters();
        return new ModbusWriteMultipleRequest<bool>
        {
            Endpoint = _endpoint!,
            SlaveId = _slaveId,
            StartAddress = startAddress,
            Values = values ?? throw new ArgumentNullException(nameof(values)),
            RegisterType = ModbusRegisterType.Coil,
            TimeoutMs = _timeoutMs,
            ProtocolType = _protocolType
        };
    }

    /// <summary>
    /// Creates a write multiple holding registers request
    /// </summary>
    /// <param name="startAddress">Starting address</param>
    /// <param name="values">Register values</param>
    /// <returns>Write request</returns>
    public ModbusWriteMultipleRequest<ushort> WriteMultipleRegisters(ushort startAddress, ushort[] values)
    {
        ValidateBaseParameters();
        return new ModbusWriteMultipleRequest<ushort>
        {
            Endpoint = _endpoint!,
            SlaveId = _slaveId,
            StartAddress = startAddress,
            Values = values ?? throw new ArgumentNullException(nameof(values)),
            RegisterType = ModbusRegisterType.HoldingRegister,
            TimeoutMs = _timeoutMs,
            ProtocolType = _protocolType
        };
    }

    /// <summary>
    /// Creates a register range for polling
    /// </summary>
    /// <param name="registerType">Register type</param>
    /// <param name="startAddress">Starting address</param>
    /// <param name="count">Number of registers</param>
    /// <param name="pollingIntervalMs">Polling interval in milliseconds</param>
    /// <returns>Register range</returns>
    public RegisterRange CreateRegisterRange(ModbusRegisterType registerType, ushort startAddress, ushort count, int pollingIntervalMs = 1000)
    {
        return new RegisterRange
        {
            RegisterType = registerType,
            StartAddress = startAddress,
            Count = count,
            SlaveId = _slaveId,
            PollingIntervalMs = pollingIntervalMs
        };
    }

    /// <summary>
    /// Validates base parameters
    /// </summary>
    private void ValidateBaseParameters()
    {
        if (_endpoint == null)
        {
            throw new InvalidOperationException("Endpoint must be set before creating requests");
        }
    }
}

/// <summary>
/// Static factory class for creating request builders
/// </summary>
public static class ModbusRequest
{
    /// <summary>
    /// Creates a new request builder
    /// </summary>
    /// <returns>New request builder instance</returns>
    public static ModbusRequestBuilder Create()
    {
        return new ModbusRequestBuilder();
    }

    /// <summary>
    /// Creates a request builder for a specific endpoint
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <returns>Request builder configured with endpoint</returns>
    public static ModbusRequestBuilder ForEndpoint(string ipAddress, int port)
    {
        return new ModbusRequestBuilder().ToEndpoint(ipAddress, port);
    }

    /// <summary>
    /// Creates a request builder for a specific endpoint
    /// </summary>
    /// <param name="endpoint">Modbus endpoint</param>
    /// <returns>Request builder configured with endpoint</returns>
    public static ModbusRequestBuilder ForEndpoint(ModbusEndpoint endpoint)
    {
        return new ModbusRequestBuilder().ToEndpoint(endpoint);
    }

    /// <summary>
    /// Creates a request builder for a specific endpoint and slave
    /// </summary>
    /// <param name="ipAddress">IP address</param>
    /// <param name="port">Port number</param>
    /// <param name="slaveId">Slave ID</param>
    /// <returns>Request builder configured with endpoint and slave</returns>
    public static ModbusRequestBuilder ForSlave(string ipAddress, int port, byte slaveId)
    {
        return new ModbusRequestBuilder()
            .ToEndpoint(ipAddress, port)
            .ForSlave(slaveId);
    }

    /// <summary>
    /// Creates a request builder for a specific endpoint and slave
    /// </summary>
    /// <param name="endpoint">Modbus endpoint</param>
    /// <param name="slaveId">Slave ID</param>
    /// <returns>Request builder configured with endpoint and slave</returns>
    public static ModbusRequestBuilder ForSlave(ModbusEndpoint endpoint, byte slaveId)
    {
        return new ModbusRequestBuilder()
            .ToEndpoint(endpoint)
            .ForSlave(slaveId);
    }
}
