using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Ngp.Communication.ModbusTcpMaster.Abstractions;
using Ngp.Communication.ModbusTcpMaster.Configuration;
using Ngp.Communication.ModbusTcpMaster.Connection;
using Ngp.Communication.ModbusTcpMaster.Engine;
using Ngp.Communication.ModbusTcpMaster.Events;

namespace Ngp.Communication.ModbusTcpMaster.FluentApi;

/// <summary>
/// Fluent API builder for creating ModbusMaster instances
/// </summary>
public class ModbusMasterBuilder
{
    private readonly ModbusConfiguration _configuration;
    private ILoggerFactory? _loggerFactory;

    /// <summary>
    /// Initializes a new instance of ModbusMasterBuilder
    /// </summary>
    public ModbusMasterBuilder()
    {
        _configuration = new ModbusConfiguration();
    }

    /// <summary>
    /// Sets the logger factory to use
    /// </summary>
    /// <param name="loggerFactory">Logger factory</param>
    /// <returns>Builder instance for chaining</returns>
    public ModbusMasterBuilder WithLogging(ILoggerFactory loggerFactory)
    {
        _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
        return this;
    }

    /// <summary>
    /// Configures connection timeouts
    /// </summary>
    /// <param name="connectionTimeoutMs">Connection timeout in milliseconds</param>
    /// <param name="requestTimeoutMs">Request timeout in milliseconds</param>
    /// <returns>Builder instance for chaining</returns>
    public ModbusMasterBuilder WithTimeouts(int connectionTimeoutMs, int requestTimeoutMs)
    {
        _configuration.DefaultConnectionTimeoutMs = connectionTimeoutMs;
        _configuration.DefaultRequestTimeoutMs = requestTimeoutMs;
        return this;
    }

    /// <summary>
    /// Configures request gap time
    /// </summary>
    /// <param name="gapMs">Gap time between requests in milliseconds</param>
    /// <returns>Builder instance for chaining</returns>
    public ModbusMasterBuilder WithRequestGap(int gapMs)
    {
        _configuration.RequestGapMs = gapMs;
        return this;
    }

    /// <summary>
    /// Configures maximum concurrent connections
    /// </summary>
    /// <param name="maxConnections">Maximum number of concurrent connections</param>
    /// <returns>Builder instance for chaining</returns>
    public ModbusMasterBuilder WithMaxConnections(int maxConnections)
    {
        _configuration.MaxConcurrentConnections = maxConnections;
        return this;
    }

    /// <summary>
    /// Configures maximum concurrent requests per connection
    /// </summary>
    /// <param name="maxRequestsPerConnection">Maximum concurrent requests per connection</param>
    /// <returns>Builder instance for chaining</returns>
    public ModbusMasterBuilder WithMaxRequestsPerConnection(int maxRequestsPerConnection)
    {
        _configuration.MaxConcurrentRequestsPerConnection = maxRequestsPerConnection;
        return this;
    }

    /// <summary>
    /// Enables or disables parallel processing
    /// </summary>
    /// <param name="enabled">Whether to enable parallel processing</param>
    /// <returns>Builder instance for chaining</returns>
    public ModbusMasterBuilder WithParallelProcessing(bool enabled = true)
    {
        _configuration.EnableParallelProcessing = enabled;
        return this;
    }

    /// <summary>
    /// Enables or disables connection pooling
    /// </summary>
    /// <param name="enabled">Whether to enable connection pooling</param>
    /// <returns>Builder instance for chaining</returns>
    public ModbusMasterBuilder WithConnectionPooling(bool enabled = true)
    {
        _configuration.EnableConnectionPooling = enabled;
        return this;
    }

    /// <summary>
    /// Enables or disables automatic reconnection
    /// </summary>
    /// <param name="enabled">Whether to enable automatic reconnection</param>
    /// <returns>Builder instance for chaining</returns>
    public ModbusMasterBuilder WithAutoReconnection(bool enabled = true)
    {
        _configuration.EnableAutoReconnection = enabled;
        return this;
    }

    /// <summary>
    /// Configures connection retry behavior
    /// </summary>
    /// <param name="configureRetry">Action to configure retry settings</param>
    /// <returns>Builder instance for chaining</returns>
    public ModbusMasterBuilder WithConnectionRetry(Action<ConnectionRetryConfiguration> configureRetry)
    {
        configureRetry?.Invoke(_configuration.ConnectionRetry);
        return this;
    }

    /// <summary>
    /// Configures register optimization
    /// </summary>
    /// <param name="configureOptimization">Action to configure optimization settings</param>
    /// <returns>Builder instance for chaining</returns>
    public ModbusMasterBuilder WithRegisterOptimization(Action<RegisterOptimizationConfiguration> configureOptimization)
    {
        configureOptimization?.Invoke(_configuration.RegisterOptimization);
        return this;
    }

    /// <summary>
    /// Configures TCP socket settings
    /// </summary>
    /// <param name="configureTcp">Action to configure TCP settings</param>
    /// <returns>Builder instance for chaining</returns>
    public ModbusMasterBuilder WithTcpSettings(Action<TcpSocketConfiguration> configureTcp)
    {
        configureTcp?.Invoke(_configuration.TcpSocket);
        return this;
    }

    /// <summary>
    /// Configures event processing
    /// </summary>
    /// <param name="configureEvents">Action to configure event processing settings</param>
    /// <returns>Builder instance for chaining</returns>
    public ModbusMasterBuilder WithEventProcessing(Action<EventProcessingConfiguration> configureEvents)
    {
        configureEvents?.Invoke(_configuration.EventProcessing);
        return this;
    }

    /// <summary>
    /// Configures all settings using a configuration object
    /// </summary>
    /// <param name="configuration">Configuration object</param>
    /// <returns>Builder instance for chaining</returns>
    public ModbusMasterBuilder WithConfiguration(ModbusConfiguration configuration)
    {
        if (configuration == null)
        {
            throw new ArgumentNullException(nameof(configuration));
        }

        // Copy configuration properties
        _configuration.DefaultConnectionTimeoutMs = configuration.DefaultConnectionTimeoutMs;
        _configuration.DefaultRequestTimeoutMs = configuration.DefaultRequestTimeoutMs;
        _configuration.RequestGapMs = configuration.RequestGapMs;
        _configuration.MaxConcurrentConnections = configuration.MaxConcurrentConnections;
        _configuration.MaxConcurrentRequestsPerConnection = configuration.MaxConcurrentRequestsPerConnection;
        _configuration.EnableParallelProcessing = configuration.EnableParallelProcessing;
        _configuration.EnableConnectionPooling = configuration.EnableConnectionPooling;
        _configuration.EnableAutoReconnection = configuration.EnableAutoReconnection;

        // Copy nested configurations
        _configuration.ConnectionRetry = configuration.ConnectionRetry;
        _configuration.RegisterOptimization = configuration.RegisterOptimization;
        _configuration.TcpSocket = configuration.TcpSocket;
        _configuration.EventProcessing = configuration.EventProcessing;

        return this;
    }

    /// <summary>
    /// Builds the ModbusMaster instance
    /// </summary>
    /// <returns>Configured ModbusMaster instance</returns>
    public IModbusMaster Build()
    {
        // Use provided logger factory or create a null logger factory
        var loggerFactory = _loggerFactory ?? NullLoggerFactory.Instance;

        // Create event dispatcher
        var eventDispatcherLogger = loggerFactory.CreateLogger<ModbusEventDispatcher>();
        var eventDispatcher = new ModbusEventDispatcher(_configuration.EventProcessing, eventDispatcherLogger);

        // Create connection manager
        var connectionManager = new ModbusConnectionManager(_configuration, loggerFactory);

        // Create main engine
        var engineLogger = loggerFactory.CreateLogger<ModbusMasterEngine>();
        var engine = new ModbusMasterEngine(_configuration, connectionManager, eventDispatcher, engineLogger);

        return engine;
    }
}

/// <summary>
/// Static factory class for creating ModbusMaster instances
/// </summary>
public static class ModbusMaster
{
    /// <summary>
    /// Creates a new ModbusMasterBuilder
    /// </summary>
    /// <returns>New builder instance</returns>
    public static ModbusMasterBuilder CreateBuilder()
    {
        return new ModbusMasterBuilder();
    }

    /// <summary>
    /// Creates a ModbusMaster with default configuration
    /// </summary>
    /// <returns>ModbusMaster instance with default settings</returns>
    public static IModbusMaster CreateDefault()
    {
        return CreateBuilder().Build();
    }

    /// <summary>
    /// Creates a ModbusMaster with custom configuration
    /// </summary>
    /// <param name="configure">Action to configure the builder</param>
    /// <returns>Configured ModbusMaster instance</returns>
    public static IModbusMaster Create(Action<ModbusMasterBuilder> configure)
    {
        var builder = CreateBuilder();
        configure?.Invoke(builder);
        return builder.Build();
    }

    /// <summary>
    /// Creates a ModbusMaster with logging support
    /// </summary>
    /// <param name="loggerFactory">Logger factory</param>
    /// <returns>ModbusMaster instance with logging</returns>
    public static IModbusMaster CreateWithLogging(ILoggerFactory loggerFactory)
    {
        return CreateBuilder()
            .WithLogging(loggerFactory)
            .Build();
    }

    /// <summary>
    /// Creates a high-performance ModbusMaster optimized for many connections
    /// </summary>
    /// <param name="maxConnections">Maximum number of connections (default: 1000)</param>
    /// <param name="loggerFactory">Optional logger factory</param>
    /// <returns>High-performance ModbusMaster instance</returns>
    public static IModbusMaster CreateHighPerformance(int maxConnections = 1000, ILoggerFactory? loggerFactory = null)
    {
        var builder = CreateBuilder()
            .WithMaxConnections(maxConnections)
            .WithMaxRequestsPerConnection(20)
            .WithParallelProcessing(true)
            .WithConnectionPooling(true)
            .WithAutoReconnection(true)
            .WithRequestGap(1) // Minimal gap for high performance
            .WithRegisterOptimization(config =>
            {
                config.EnableRangeOptimization = true;
                config.MaxRegisterGap = 5; // Aggressive optimization
            })
            .WithEventProcessing(config =>
            {
                config.EnableParallelEventProcessing = true;
                config.EventProcessorThreads = Environment.ProcessorCount * 2;
            });

        if (loggerFactory != null)
        {
            builder.WithLogging(loggerFactory);
        }

        return builder.Build();
    }
}
