using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Events;

namespace Ngp.Communication.ModbusTcpMaster.Abstractions;

/// <summary>
/// Main interface for Modbus TCP Master operations
/// </summary>
public interface IModbusMaster : IDisposable
{
    /// <summary>
    /// Event triggered when connection status changes
    /// </summary>
    event EventHandler<ConnectionStatusEventArgs>? ConnectionStatusChanged;

    /// <summary>
    /// Event triggered when data is updated
    /// </summary>
    event EventHandler<DataUpdatedEventArgs>? DataUpdated;

    /// <summary>
    /// Event triggered when an error occurs
    /// </summary>
    event EventHandler<ModbusErrorEventArgs>? ErrorOccurred;

    /// <summary>
    /// Gets the current connection status for a specific endpoint
    /// </summary>
    /// <param name="endpoint">The endpoint to check</param>
    /// <returns>Connection status</returns>
    ConnectionStatus GetConnectionStatus(ModbusEndpoint endpoint);

    /// <summary>
    /// Connects to a Modbus slave device
    /// </summary>
    /// <param name="endpoint">The endpoint to connect to</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the connection operation</returns>
    Task<bool> ConnectAsync(ModbusEndpoint endpoint, CancellationToken cancellationToken = default);

    /// <summary>
    /// Disconnects from a Modbus slave device
    /// </summary>
    /// <param name="endpoint">The endpoint to disconnect from</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the disconnection operation</returns>
    Task DisconnectAsync(ModbusEndpoint endpoint, CancellationToken cancellationToken = default);

    /// <summary>
    /// Reads coils from a Modbus slave
    /// </summary>
    /// <param name="request">The read request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Read response</returns>
    Task<ModbusResponse<bool[]>> ReadCoilsAsync(ModbusReadRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Reads discrete inputs from a Modbus slave
    /// </summary>
    /// <param name="request">The read request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Read response</returns>
    Task<ModbusResponse<bool[]>> ReadDiscreteInputsAsync(ModbusReadRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Reads holding registers from a Modbus slave
    /// </summary>
    /// <param name="request">The read request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Read response</returns>
    Task<ModbusResponse<ushort[]>> ReadHoldingRegistersAsync(ModbusReadRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Reads input registers from a Modbus slave
    /// </summary>
    /// <param name="request">The read request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Read response</returns>
    Task<ModbusResponse<ushort[]>> ReadInputRegistersAsync(ModbusReadRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Writes a single coil to a Modbus slave
    /// </summary>
    /// <param name="request">The write request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Write response</returns>
    Task<ModbusResponse> WriteSingleCoilAsync(ModbusWriteSingleRequest<bool> request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Writes a single holding register to a Modbus slave
    /// </summary>
    /// <param name="request">The write request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Write response</returns>
    Task<ModbusResponse> WriteSingleRegisterAsync(ModbusWriteSingleRequest<ushort> request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Writes multiple coils to a Modbus slave
    /// </summary>
    /// <param name="request">The write request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Write response</returns>
    Task<ModbusResponse> WriteMultipleCoilsAsync(ModbusWriteMultipleRequest<bool> request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Writes multiple holding registers to a Modbus slave
    /// </summary>
    /// <param name="request">The write request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Write response</returns>
    Task<ModbusResponse> WriteMultipleRegistersAsync(ModbusWriteMultipleRequest<ushort> request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Starts continuous polling for specified register ranges
    /// </summary>
    /// <param name="endpoint">The endpoint to poll</param>
    /// <param name="registerRanges">The register ranges to poll</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the polling operation</returns>
    Task StartPollingAsync(ModbusEndpoint endpoint, IEnumerable<RegisterRange> registerRanges, CancellationToken cancellationToken = default);

    /// <summary>
    /// Stops continuous polling for an endpoint
    /// </summary>
    /// <param name="endpoint">The endpoint to stop polling</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the stop operation</returns>
    Task StopPollingAsync(ModbusEndpoint endpoint, CancellationToken cancellationToken = default);
}
