using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Abstractions;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Configuration;

namespace Ngp.Communication.ModbusTcpMaster.Connection;

/// <summary>
/// Manages multiple Modbus TCP connections
/// </summary>
public class ModbusConnectionManager : IModbusConnectionManager
{
    private readonly ILogger<ModbusConnectionManager> _logger;
    private readonly ILoggerFactory _loggerFactory;
    private readonly ModbusConfiguration _configuration;
    private readonly ConcurrentDictionary<string, IModbusConnection> _connections;
    private readonly SemaphoreSlim _connectionSemaphore;
    private readonly Timer _healthCheckTimer;
    private bool _disposed;

    /// <summary>
    /// Initializes a new instance of ModbusConnectionManager
    /// </summary>
    /// <param name="configuration">Configuration options</param>
    /// <param name="loggerFactory">Logger factory</param>
    public ModbusConnectionManager(
        ModbusConfiguration configuration,
        ILoggerFactory loggerFactory)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
        _logger = _loggerFactory.CreateLogger<ModbusConnectionManager>();

        _connections = new ConcurrentDictionary<string, IModbusConnection>();
        _connectionSemaphore = new SemaphoreSlim(_configuration.MaxConcurrentConnections, _configuration.MaxConcurrentConnections);

        // Start health check timer
        _healthCheckTimer = new Timer(PerformHealthCheck, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));

        _logger.LogInformation("ModbusConnectionManager initialized with max {MaxConnections} connections", _configuration.MaxConcurrentConnections);
    }

    /// <summary>
    /// Gets or creates a connection for the specified endpoint
    /// </summary>
    /// <param name="endpoint">The endpoint to get connection for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The connection instance</returns>
    public async Task<IModbusConnection> GetConnectionAsync(ModbusEndpoint endpoint, CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(ModbusConnectionManager));
        }

        var key = GetConnectionKey(endpoint);

        // Try to get existing connection
        if (_connections.TryGetValue(key, out var existingConnection))
        {
            // Check if connection is healthy
            if (await existingConnection.IsHealthyAsync(cancellationToken))
            {
                return existingConnection;
            }

            // Remove unhealthy connection
            _logger.LogWarning("Removing unhealthy connection to {Endpoint}", endpoint);
            await RemoveConnectionInternalAsync(key, existingConnection);
        }

        // Create new connection
        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            // Double-check pattern
            if (_connections.TryGetValue(key, out existingConnection))
            {
                if (await existingConnection.IsHealthyAsync(cancellationToken))
                {
                    return existingConnection;
                }
                await RemoveConnectionInternalAsync(key, existingConnection);
            }

            // Create new connection
            var connectionLogger = _loggerFactory.CreateLogger<ModbusTcpConnection>();
            var newConnection = new ModbusTcpConnection(endpoint, _configuration, connectionLogger);

            // Subscribe to status changes
            newConnection.StatusChanged += OnConnectionStatusChanged;

            // Add to collection
            _connections[key] = newConnection;

            _logger.LogInformation("Created new connection to {Endpoint}", endpoint);
            return newConnection;
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    /// <summary>
    /// Removes and disposes a connection for the specified endpoint
    /// </summary>
    /// <param name="endpoint">The endpoint to remove connection for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the removal operation</returns>
    public async Task RemoveConnectionAsync(ModbusEndpoint endpoint, CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            return;
        }

        var key = GetConnectionKey(endpoint);
        if (_connections.TryRemove(key, out var connection))
        {
            await RemoveConnectionInternalAsync(key, connection);
            _logger.LogInformation("Removed connection to {Endpoint}", endpoint);
        }
    }

    /// <summary>
    /// Gets all active connections
    /// </summary>
    /// <returns>Collection of active connections</returns>
    public IEnumerable<IModbusConnection> GetActiveConnections()
    {
        if (_disposed)
        {
            return Enumerable.Empty<IModbusConnection>();
        }

        return _connections.Values.ToList();
    }

    /// <summary>
    /// Gets connection status for a specific endpoint
    /// </summary>
    /// <param name="endpoint">The endpoint to check</param>
    /// <returns>Connection status</returns>
    public ConnectionStatus GetConnectionStatus(ModbusEndpoint endpoint)
    {
        if (_disposed)
        {
            return ConnectionStatus.Disconnected;
        }

        var key = GetConnectionKey(endpoint);
        if (_connections.TryGetValue(key, out var connection))
        {
            return connection.Status;
        }

        return ConnectionStatus.Disconnected;
    }

    /// <summary>
    /// Disposes all connections and releases resources
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
        {
            return;
        }

        _disposed = true;

        try
        {
            _healthCheckTimer.Dispose();

            // Dispose all connections
            var connections = _connections.Values.ToList();
            _connections.Clear();

            Parallel.ForEach(connections, connection =>
            {
                try
                {
                    connection.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error disposing connection to {Endpoint}", connection.Endpoint);
                }
            });

            _connectionSemaphore.Dispose();

            _logger.LogInformation("ModbusConnectionManager disposed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during ModbusConnectionManager disposal");
        }
    }

    /// <summary>
    /// Gets a unique key for the endpoint
    /// </summary>
    /// <param name="endpoint">The endpoint</param>
    /// <returns>Unique key string</returns>
    private static string GetConnectionKey(ModbusEndpoint endpoint)
    {
        return $"{endpoint.IpAddress}:{endpoint.Port}";
    }

    /// <summary>
    /// Handles connection status changes
    /// </summary>
    /// <param name="sender">The connection that changed status</param>
    /// <param name="e">Event arguments</param>
    private void OnConnectionStatusChanged(object? sender, Events.ConnectionStatusEventArgs e)
    {
        _logger.LogDebug("Connection status changed for {Endpoint}: {PreviousStatus} -> {CurrentStatus}",
            e.Endpoint, e.PreviousStatus, e.CurrentStatus);

        // If connection failed, consider removing it
        if (e.CurrentStatus == ConnectionStatus.Error && _configuration.EnableAutoReconnection)
        {
            _ = Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(TimeSpan.FromSeconds(5)); // Wait before removing
                    var key = GetConnectionKey(e.Endpoint);
                    if (_connections.TryGetValue(key, out var connection) && connection.Status == ConnectionStatus.Error)
                    {
                        await RemoveConnectionAsync(e.Endpoint);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error handling connection status change for {Endpoint}", e.Endpoint);
                }
            });
        }
    }

    /// <summary>
    /// Removes a connection internally
    /// </summary>
    /// <param name="key">Connection key</param>
    /// <param name="connection">Connection to remove</param>
    private async Task RemoveConnectionInternalAsync(string key, IModbusConnection connection)
    {
        try
        {
            // Unsubscribe from events
            connection.StatusChanged -= OnConnectionStatusChanged;

            // Disconnect and dispose
            if (connection.IsConnected)
            {
                await connection.DisconnectAsync();
            }
            connection.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing connection {Key}", key);
        }
    }

    /// <summary>
    /// Performs periodic health checks on all connections
    /// </summary>
    /// <param name="state">Timer state (not used)</param>
    private void PerformHealthCheck(object? state)
    {
        if (_disposed)
        {
            return;
        }

        _ = Task.Run(async () =>
        {
            try
            {
                var connections = _connections.ToList();
                var unhealthyConnections = new List<KeyValuePair<string, IModbusConnection>>();

                foreach (var kvp in connections)
                {
                    try
                    {
                        if (!await kvp.Value.IsHealthyAsync())
                        {
                            unhealthyConnections.Add(kvp);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Health check failed for connection {Key}", kvp.Key);
                        unhealthyConnections.Add(kvp);
                    }
                }

                // Remove unhealthy connections
                foreach (var kvp in unhealthyConnections)
                {
                    _logger.LogWarning("Removing unhealthy connection {Key}", kvp.Key);
                    _connections.TryRemove(kvp.Key, out _);
                    await RemoveConnectionInternalAsync(kvp.Key, kvp.Value);
                }

                if (unhealthyConnections.Count > 0)
                {
                    _logger.LogInformation("Health check completed. Removed {Count} unhealthy connections", unhealthyConnections.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during health check");
            }
        });
    }
}
