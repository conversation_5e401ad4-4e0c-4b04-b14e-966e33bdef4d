using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Abstractions;
using Ngp.Communication.ModbusTcpMaster.FluentApi;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Events;

namespace Ngp.Communication.ModbusTcpMaster.Examples;

/// <summary>
/// Basic usage examples for the Modbus TCP Master library
/// </summary>
public class BasicUsageExample
{
    /// <summary>
    /// Example of basic read operations
    /// </summary>
    public static async Task BasicReadExample()
    {
        // Create a Modbus master with default configuration
        using var master = ModbusMaster.CreateDefault();

        // Define endpoint
        var endpoint = ModbusEndpoint.Create("*************", 502);

        try
        {
            // Connect to the device
            var connected = await master.ConnectAsync(endpoint);
            if (!connected)
            {
                Console.WriteLine("Failed to connect to device");
                return;
            }

            // Create read requests using fluent API
            var coilRequest = ModbusRequest
                .ForSlave(endpoint, slaveId: 1)
                .ReadCoils(startAddress: 0, count: 10);

            var holdingRegisterRequest = ModbusRequest
                .ForSlave(endpoint, slaveId: 1)
                .ReadHoldingRegisters(startAddress: 0, count: 5);

            // Execute read operations
            var coilResponse = await master.ReadCoilsAsync(coilRequest);
            var registerResponse = await master.ReadHoldingRegistersAsync(holdingRegisterRequest);

            // Process results
            if (coilResponse.IsSuccess && coilResponse.Data != null)
            {
                Console.WriteLine($"Read {coilResponse.Data.Length} coils:");
                for (int i = 0; i < coilResponse.Data.Length; i++)
                {
                    Console.WriteLine($"  Coil {i}: {coilResponse.Data[i]}");
                }
            }

            if (registerResponse.IsSuccess && registerResponse.Data != null)
            {
                Console.WriteLine($"Read {registerResponse.Data.Length} holding registers:");
                for (int i = 0; i < registerResponse.Data.Length; i++)
                {
                    Console.WriteLine($"  Register {i}: {registerResponse.Data[i]}");
                }
            }
        }
        finally
        {
            await master.DisconnectAsync(endpoint);
        }
    }

    /// <summary>
    /// Example of write operations
    /// </summary>
    public static async Task BasicWriteExample()
    {
        using var master = ModbusMaster.CreateDefault();
        var endpoint = ModbusEndpoint.Create("*************", 502);

        try
        {
            await master.ConnectAsync(endpoint);

            // Write single coil
            var singleCoilRequest = ModbusRequest
                .ForSlave(endpoint, slaveId: 1)
                .WriteSingleCoil(address: 0, value: true);

            var singleCoilResponse = await master.WriteSingleCoilAsync(singleCoilRequest);
            Console.WriteLine($"Single coil write: {(singleCoilResponse.IsSuccess ? "Success" : "Failed")}");

            // Write single register
            var singleRegisterRequest = ModbusRequest
                .ForSlave(endpoint, slaveId: 1)
                .WriteSingleRegister(address: 0, value: 1234);

            var singleRegisterResponse = await master.WriteSingleRegisterAsync(singleRegisterRequest);
            Console.WriteLine($"Single register write: {(singleRegisterResponse.IsSuccess ? "Success" : "Failed")}");

            // Write multiple coils
            var multipleCoilsRequest = ModbusRequest
                .ForSlave(endpoint, slaveId: 1)
                .WriteMultipleCoils(startAddress: 10, values: new[] { true, false, true, false, true });

            var multipleCoilsResponse = await master.WriteMultipleCoilsAsync(multipleCoilsRequest);
            Console.WriteLine($"Multiple coils write: {(multipleCoilsResponse.IsSuccess ? "Success" : "Failed")}");

            // Write multiple registers
            var multipleRegistersRequest = ModbusRequest
                .ForSlave(endpoint, slaveId: 1)
                .WriteMultipleRegisters(startAddress: 10, values: new ushort[] { 100, 200, 300, 400, 500 });

            var multipleRegistersResponse = await master.WriteMultipleRegistersAsync(multipleRegistersRequest);
            Console.WriteLine($"Multiple registers write: {(multipleRegistersResponse.IsSuccess ? "Success" : "Failed")}");
        }
        finally
        {
            await master.DisconnectAsync(endpoint);
        }
    }

    /// <summary>
    /// Example of continuous polling
    /// </summary>
    public static async Task PollingExample()
    {
        using var master = ModbusMaster.CreateDefault();
        var endpoint = ModbusEndpoint.Create("*************", 502);

        // Subscribe to data update events
        master.DataUpdated += OnDataUpdated;
        master.ErrorOccurred += OnErrorOccurred;

        try
        {
            await master.ConnectAsync(endpoint);

            // Define register ranges to poll
            var registerRanges = new[]
            {
                new RegisterRange
                {
                    RegisterType = ModbusRegisterType.Coil,
                    StartAddress = 0,
                    Count = 10,
                    SlaveId = 1,
                    PollingIntervalMs = 1000
                },
                new RegisterRange
                {
                    RegisterType = ModbusRegisterType.HoldingRegister,
                    StartAddress = 0,
                    Count = 5,
                    SlaveId = 1,
                    PollingIntervalMs = 2000
                }
            };

            // Start polling
            await master.StartPollingAsync(endpoint, registerRanges);

            Console.WriteLine("Polling started. Press any key to stop...");
            Console.ReadKey();

            // Stop polling
            await master.StopPollingAsync(endpoint);
        }
        finally
        {
            await master.DisconnectAsync(endpoint);
        }
    }

    /// <summary>
    /// Example of high-performance configuration for many devices
    /// </summary>
    public static async Task HighPerformanceExample()
    {
        // Create high-performance master for up to 1000 connections
        using var loggerFactory = Microsoft.Extensions.Logging.LoggerFactory.Create(builder => builder.AddConsole());
        using var master = ModbusMaster.CreateHighPerformance(maxConnections: 1000, loggerFactory);

        // Subscribe to events
        master.ConnectionStatusChanged += OnConnectionStatusChanged;
        master.DataUpdated += OnDataUpdated;
        master.ErrorOccurred += OnErrorOccurred;

        var endpoints = new List<ModbusEndpoint>();
        
        // Create multiple endpoints (simulating many devices)
        for (int i = 1; i <= 10; i++)
        {
            endpoints.Add(ModbusEndpoint.Create($"192.168.1.{i}", 502));
        }

        try
        {
            // Connect to all devices in parallel
            var connectionTasks = endpoints.Select(endpoint => master.ConnectAsync(endpoint));
            await Task.WhenAll(connectionTasks);

            // Start polling for all devices
            var pollingTasks = endpoints.Select(endpoint =>
            {
                var ranges = new[]
                {
                    new RegisterRange
                    {
                        RegisterType = ModbusRegisterType.HoldingRegister,
                        StartAddress = 0,
                        Count = 10,
                        SlaveId = 1,
                        PollingIntervalMs = 500
                    }
                };
                return master.StartPollingAsync(endpoint, ranges);
            });

            await Task.WhenAll(pollingTasks);

            Console.WriteLine($"Started polling {endpoints.Count} devices. Press any key to stop...");
            Console.ReadKey();

            // Stop all polling
            var stopTasks = endpoints.Select(endpoint => master.StopPollingAsync(endpoint));
            await Task.WhenAll(stopTasks);
        }
        finally
        {
            // Disconnect from all devices
            var disconnectTasks = endpoints.Select(endpoint => master.DisconnectAsync(endpoint));
            await Task.WhenAll(disconnectTasks);
        }
    }

    /// <summary>
    /// Example of custom configuration
    /// </summary>
    public static IModbusMaster CreateCustomMaster()
    {
        return ModbusMaster.Create(builder => builder
            .WithTimeouts(connectionTimeoutMs: 10000, requestTimeoutMs: 8000)
            .WithRequestGap(50) // 50ms gap between requests
            .WithMaxConnections(500)
            .WithMaxRequestsPerConnection(5)
            .WithParallelProcessing(true)
            .WithConnectionRetry(retry =>
            {
                retry.MaxRetryAttempts = 5;
                retry.InitialRetryDelayMs = 2000;
                retry.UseExponentialBackoff = true;
            })
            .WithRegisterOptimization(opt =>
            {
                opt.EnableRangeOptimization = true;
                opt.MaxRegisterGap = 20;
                opt.MaxHoldingRegistersPerRequest = 100;
            })
            .WithTcpSettings(tcp =>
            {
                tcp.ReceiveBufferSize = 16384;
                tcp.SendBufferSize = 16384;
                tcp.NoDelay = true;
            })
            .WithEventProcessing(events =>
            {
                events.EnableParallelEventProcessing = true;
                events.EventProcessorThreads = 4;
                events.MaxEventQueueSize = 5000;
            }));
    }

    // Event handlers
    private static void OnConnectionStatusChanged(object? sender, ConnectionStatusEventArgs e)
    {
        Console.WriteLine($"Connection status changed for {e.Endpoint}: {e.PreviousStatus} -> {e.CurrentStatus}");
    }

    private static void OnDataUpdated(object? sender, DataUpdatedEventArgs e)
    {
        Console.WriteLine($"Data updated for {e.Endpoint}, Slave {e.SlaveId}, {e.RegisterType} at address {e.StartAddress}");
    }

    private static void OnErrorOccurred(object? sender, ModbusErrorEventArgs e)
    {
        Console.WriteLine($"Error occurred for {e.Endpoint}: {e.ErrorType} - {e.Message}");
    }
}
