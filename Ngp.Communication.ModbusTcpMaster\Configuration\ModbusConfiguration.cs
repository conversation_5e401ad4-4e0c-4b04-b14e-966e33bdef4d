namespace Ngp.Communication.ModbusTcpMaster.Configuration;

/// <summary>
/// Configuration options for Modbus TCP Master
/// </summary>
public class ModbusConfiguration
{
    /// <summary>
    /// Default connection timeout in milliseconds
    /// </summary>
    public int DefaultConnectionTimeoutMs { get; set; } = 5000;

    /// <summary>
    /// Default request timeout in milliseconds
    /// </summary>
    public int DefaultRequestTimeoutMs { get; set; } = 5000;

    /// <summary>
    /// Gap time between requests in milliseconds
    /// </summary>
    public int RequestGapMs { get; set; } = 10;

    /// <summary>
    /// Maximum number of concurrent connections
    /// </summary>
    public int MaxConcurrentConnections { get; set; } = 1000;

    /// <summary>
    /// Maximum number of concurrent requests per connection
    /// </summary>
    public int MaxConcurrentRequestsPerConnection { get; set; } = 10;

    /// <summary>
    /// Connection retry configuration
    /// </summary>
    public ConnectionRetryConfiguration ConnectionRetry { get; set; } = new();

    /// <summary>
    /// Register optimization configuration
    /// </summary>
    public RegisterOptimizationConfiguration RegisterOptimization { get; set; } = new();

    /// <summary>
    /// TCP socket configuration
    /// </summary>
    public TcpSocketConfiguration TcpSocket { get; set; } = new();

    /// <summary>
    /// Event processing configuration
    /// </summary>
    public EventProcessingConfiguration EventProcessing { get; set; } = new();

    /// <summary>
    /// Whether to enable parallel processing for requests
    /// </summary>
    public bool EnableParallelProcessing { get; set; } = true;

    /// <summary>
    /// Whether to enable connection pooling
    /// </summary>
    public bool EnableConnectionPooling { get; set; } = true;

    /// <summary>
    /// Whether to enable automatic reconnection
    /// </summary>
    public bool EnableAutoReconnection { get; set; } = true;
}

/// <summary>
/// Configuration for connection retry behavior
/// </summary>
public class ConnectionRetryConfiguration
{
    /// <summary>
    /// Maximum number of retry attempts
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Initial retry delay in milliseconds
    /// </summary>
    public int InitialRetryDelayMs { get; set; } = 1000;

    /// <summary>
    /// Maximum retry delay in milliseconds
    /// </summary>
    public int MaxRetryDelayMs { get; set; } = 30000;

    /// <summary>
    /// Retry delay multiplier for exponential backoff
    /// </summary>
    public double RetryDelayMultiplier { get; set; } = 2.0;

    /// <summary>
    /// Whether to use exponential backoff for retry delays
    /// </summary>
    public bool UseExponentialBackoff { get; set; } = true;
}

/// <summary>
/// Configuration for register optimization
/// </summary>
public class RegisterOptimizationConfiguration
{
    /// <summary>
    /// Maximum number of coils to read in a single request
    /// </summary>
    public ushort MaxCoilsPerRequest { get; set; } = 2000;

    /// <summary>
    /// Maximum number of discrete inputs to read in a single request
    /// </summary>
    public ushort MaxDiscreteInputsPerRequest { get; set; } = 2000;

    /// <summary>
    /// Maximum number of holding registers to read in a single request
    /// </summary>
    public ushort MaxHoldingRegistersPerRequest { get; set; } = 125;

    /// <summary>
    /// Maximum number of input registers to read in a single request
    /// </summary>
    public ushort MaxInputRegistersPerRequest { get; set; } = 125;

    /// <summary>
    /// Maximum gap between registers to consider them as continuous
    /// </summary>
    public ushort MaxRegisterGap { get; set; } = 10;

    /// <summary>
    /// Whether to enable register range optimization
    /// </summary>
    public bool EnableRangeOptimization { get; set; } = true;
}

/// <summary>
/// Configuration for TCP socket behavior
/// </summary>
public class TcpSocketConfiguration
{
    /// <summary>
    /// Socket receive buffer size
    /// </summary>
    public int ReceiveBufferSize { get; set; } = 8192;

    /// <summary>
    /// Socket send buffer size
    /// </summary>
    public int SendBufferSize { get; set; } = 8192;

    /// <summary>
    /// Whether to enable TCP keep-alive
    /// </summary>
    public bool EnableKeepAlive { get; set; } = true;

    /// <summary>
    /// TCP keep-alive time in milliseconds
    /// </summary>
    public int KeepAliveTimeMs { get; set; } = 7200000; // 2 hours

    /// <summary>
    /// TCP keep-alive interval in milliseconds
    /// </summary>
    public int KeepAliveIntervalMs { get; set; } = 1000;

    /// <summary>
    /// Whether to disable Nagle algorithm
    /// </summary>
    public bool NoDelay { get; set; } = true;

    /// <summary>
    /// Socket linger time in seconds (-1 to disable)
    /// </summary>
    public int LingerTimeSeconds { get; set; } = -1;
}

/// <summary>
/// Configuration for event processing
/// </summary>
public class EventProcessingConfiguration
{
    /// <summary>
    /// Maximum number of events to queue before dropping
    /// </summary>
    public int MaxEventQueueSize { get; set; } = 10000;

    /// <summary>
    /// Number of worker threads for event processing
    /// </summary>
    public int EventProcessorThreads { get; set; } = Environment.ProcessorCount;

    /// <summary>
    /// Whether to process events in parallel
    /// </summary>
    public bool EnableParallelEventProcessing { get; set; } = true;

    /// <summary>
    /// Event processing timeout in milliseconds
    /// </summary>
    public int EventProcessingTimeoutMs { get; set; } = 5000;
}
