namespace Ngp.Communication.ModbusTcpMaster.Protocol;

/// <summary>
/// Modbus function codes as defined in the Modbus specification
/// </summary>
public static class ModbusFunctionCodes
{
    /// <summary>
    /// Read Coils (0x01)
    /// </summary>
    public const byte ReadCoils = 0x01;

    /// <summary>
    /// Read Discrete Inputs (0x02)
    /// </summary>
    public const byte ReadDiscreteInputs = 0x02;

    /// <summary>
    /// Read Holding Registers (0x03)
    /// </summary>
    public const byte ReadHoldingRegisters = 0x03;

    /// <summary>
    /// Read Input Registers (0x04)
    /// </summary>
    public const byte ReadInputRegisters = 0x04;

    /// <summary>
    /// Write Single Coil (0x05)
    /// </summary>
    public const byte WriteSingleCoil = 0x05;

    /// <summary>
    /// Write Single Register (0x06)
    /// </summary>
    public const byte WriteSingleRegister = 0x06;

    /// <summary>
    /// Read Exception Status (0x07)
    /// </summary>
    public const byte ReadExceptionStatus = 0x07;

    /// <summary>
    /// Diagnostics (0x08)
    /// </summary>
    public const byte Diagnostics = 0x08;

    /// <summary>
    /// Get Comm Event Counter (0x0B)
    /// </summary>
    public const byte GetCommEventCounter = 0x0B;

    /// <summary>
    /// Get Comm Event Log (0x0C)
    /// </summary>
    public const byte GetCommEventLog = 0x0C;

    /// <summary>
    /// Write Multiple Coils (0x0F)
    /// </summary>
    public const byte WriteMultipleCoils = 0x0F;

    /// <summary>
    /// Write Multiple Registers (0x10)
    /// </summary>
    public const byte WriteMultipleRegisters = 0x10;

    /// <summary>
    /// Report Slave ID (0x11)
    /// </summary>
    public const byte ReportSlaveId = 0x11;

    /// <summary>
    /// Read File Record (0x14)
    /// </summary>
    public const byte ReadFileRecord = 0x14;

    /// <summary>
    /// Write File Record (0x15)
    /// </summary>
    public const byte WriteFileRecord = 0x15;

    /// <summary>
    /// Mask Write Register (0x16)
    /// </summary>
    public const byte MaskWriteRegister = 0x16;

    /// <summary>
    /// Read/Write Multiple Registers (0x17)
    /// </summary>
    public const byte ReadWriteMultipleRegisters = 0x17;

    /// <summary>
    /// Read FIFO Queue (0x18)
    /// </summary>
    public const byte ReadFifoQueue = 0x18;

    /// <summary>
    /// Encapsulated Interface Transport (0x2B)
    /// </summary>
    public const byte EncapsulatedInterfaceTransport = 0x2B;

    /// <summary>
    /// Error flag mask (0x80)
    /// </summary>
    public const byte ErrorFlag = 0x80;

    /// <summary>
    /// Checks if a function code represents an error response
    /// </summary>
    /// <param name="functionCode">The function code to check</param>
    /// <returns>True if it's an error response, false otherwise</returns>
    public static bool IsErrorResponse(byte functionCode)
    {
        return (functionCode & ErrorFlag) == ErrorFlag;
    }

    /// <summary>
    /// Gets the original function code from an error response
    /// </summary>
    /// <param name="errorFunctionCode">The error function code</param>
    /// <returns>The original function code</returns>
    public static byte GetOriginalFunctionCode(byte errorFunctionCode)
    {
        return (byte)(errorFunctionCode & ~ErrorFlag);
    }

    /// <summary>
    /// Creates an error function code from the original function code
    /// </summary>
    /// <param name="originalFunctionCode">The original function code</param>
    /// <returns>The error function code</returns>
    public static byte CreateErrorFunctionCode(byte originalFunctionCode)
    {
        return (byte)(originalFunctionCode | ErrorFlag);
    }
}

/// <summary>
/// Modbus exception codes as defined in the Modbus specification
/// </summary>
public static class ModbusExceptionCodes
{
    /// <summary>
    /// Illegal Function (0x01)
    /// The function code received in the query is not an allowable action for the slave
    /// </summary>
    public const byte IllegalFunction = 0x01;

    /// <summary>
    /// Illegal Data Address (0x02)
    /// The data address received in the query is not an allowable address for the slave
    /// </summary>
    public const byte IllegalDataAddress = 0x02;

    /// <summary>
    /// Illegal Data Value (0x03)
    /// A value contained in the query data field is not an allowable value for the slave
    /// </summary>
    public const byte IllegalDataValue = 0x03;

    /// <summary>
    /// Slave Device Failure (0x04)
    /// An unrecoverable error occurred while the slave was attempting to perform the requested action
    /// </summary>
    public const byte SlaveDeviceFailure = 0x04;

    /// <summary>
    /// Acknowledge (0x05)
    /// The slave has accepted the request and is processing it, but a long duration of time will be required to do so
    /// </summary>
    public const byte Acknowledge = 0x05;

    /// <summary>
    /// Slave Device Busy (0x06)
    /// The slave is engaged in processing a long-duration program command
    /// </summary>
    public const byte SlaveDeviceBusy = 0x06;

    /// <summary>
    /// Negative Acknowledge (0x07)
    /// The slave cannot perform the program function received in the query
    /// </summary>
    public const byte NegativeAcknowledge = 0x07;

    /// <summary>
    /// Memory Parity Error (0x08)
    /// The slave detected a parity error in the extended file area
    /// </summary>
    public const byte MemoryParityError = 0x08;

    /// <summary>
    /// Gateway Path Unavailable (0x0A)
    /// Gateway was unable to allocate an internal communication path from the input port to the output port
    /// </summary>
    public const byte GatewayPathUnavailable = 0x0A;

    /// <summary>
    /// Gateway Target Device Failed to Respond (0x0B)
    /// No response was obtained from the target device
    /// </summary>
    public const byte GatewayTargetDeviceFailedToRespond = 0x0B;

    /// <summary>
    /// Gets a human-readable description of the exception code
    /// </summary>
    /// <param name="exceptionCode">The exception code</param>
    /// <returns>Description of the exception</returns>
    public static string GetDescription(byte exceptionCode)
    {
        return exceptionCode switch
        {
            IllegalFunction => "Illegal Function - The function code is not supported",
            IllegalDataAddress => "Illegal Data Address - The data address is not valid",
            IllegalDataValue => "Illegal Data Value - The data value is not valid",
            SlaveDeviceFailure => "Slave Device Failure - An unrecoverable error occurred",
            Acknowledge => "Acknowledge - Request accepted but processing will take time",
            SlaveDeviceBusy => "Slave Device Busy - The device is busy processing another request",
            NegativeAcknowledge => "Negative Acknowledge - The device cannot perform the requested function",
            MemoryParityError => "Memory Parity Error - Parity error detected in memory",
            GatewayPathUnavailable => "Gateway Path Unavailable - Internal communication path unavailable",
            GatewayTargetDeviceFailedToRespond => "Gateway Target Device Failed to Respond - No response from target device",
            _ => $"Unknown Exception Code: 0x{exceptionCode:X2}"
        };
    }
}
