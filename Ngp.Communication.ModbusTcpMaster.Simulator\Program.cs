using Microsoft.Extensions.Logging;
using System.Net;
using System.Net.Sockets;

namespace Ngp.Communication.ModbusTcpMaster.Simulator;

/// <summary>
/// Simple Modbus TCP Slave Simulator for testing purposes
/// </summary>
class Program
{
    private static readonly ILoggerFactory LoggerFactory = Microsoft.Extensions.Logging.LoggerFactory.Create(builder => builder.AddConsole());
    private static readonly ILogger Logger = LoggerFactory.CreateLogger<Program>();

    static async Task Main(string[] args)
    {
        Console.WriteLine("Modbus TCP Slave Simulator");
        Console.WriteLine("==========================");
        
        var port = args.Length > 0 && int.TryParse(args[0], out var p) ? p : 502;
        
        var simulator = new ModbusTcpSimulator(port, LoggerFactory.CreateLogger<ModbusTcpSimulator>());
        
        Console.WriteLine($"Starting Modbus TCP Slave on port {port}");
        Console.WriteLine("Press 'q' to quit");
        
        await simulator.StartAsync();
        
        while (true)
        {
            var key = Console.ReadKey(true);
            if (key.KeyChar == 'q' || key.KeyChar == 'Q')
            {
                break;
            }
        }
        
        await simulator.StopAsync();
        Console.WriteLine("Simulator stopped.");
    }
}

/// <summary>
/// Simple Modbus TCP Slave Simulator
/// </summary>
public class ModbusTcpSimulator
{
    private readonly int _port;
    private readonly ILogger<ModbusTcpSimulator> _logger;
    private TcpListener? _listener;
    private CancellationTokenSource? _cancellationTokenSource;
    private readonly List<Task> _clientTasks = new();
    
    // Simulated data storage
    private readonly bool[] _coils = new bool[10000];
    private readonly bool[] _discreteInputs = new bool[10000];
    private readonly ushort[] _holdingRegisters = new ushort[10000];
    private readonly ushort[] _inputRegisters = new ushort[10000];

    public ModbusTcpSimulator(int port, ILogger<ModbusTcpSimulator> logger)
    {
        _port = port;
        _logger = logger;
        
        // Initialize some test data
        InitializeTestData();
    }

    private void InitializeTestData()
    {
        // Initialize some coils
        for (int i = 0; i < 10; i++)
        {
            _coils[i] = i % 2 == 0;
        }
        
        // Initialize some holding registers
        for (int i = 0; i < 10; i++)
        {
            _holdingRegisters[i] = (ushort)(i * 100);
        }
        
        // Initialize some input registers
        for (int i = 0; i < 10; i++)
        {
            _inputRegisters[i] = (ushort)(i * 50);
        }
    }

    public async Task StartAsync()
    {
        _cancellationTokenSource = new CancellationTokenSource();
        _listener = new TcpListener(IPAddress.Any, _port);
        _listener.Start();
        
        _logger.LogInformation("Modbus TCP Simulator started on port {Port}", _port);
        
        _ = Task.Run(async () =>
        {
            while (!_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    var tcpClient = await _listener.AcceptTcpClientAsync();
                    var clientTask = HandleClientAsync(tcpClient, _cancellationTokenSource.Token);
                    _clientTasks.Add(clientTask);
                }
                catch (ObjectDisposedException)
                {
                    // Expected when stopping
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error accepting client connection");
                }
            }
        }, _cancellationTokenSource.Token);
    }

    public async Task StopAsync()
    {
        _cancellationTokenSource?.Cancel();
        _listener?.Stop();
        
        await Task.WhenAll(_clientTasks);
        
        _cancellationTokenSource?.Dispose();
        _logger.LogInformation("Modbus TCP Simulator stopped");
    }

    private async Task HandleClientAsync(TcpClient client, CancellationToken cancellationToken)
    {
        var clientEndpoint = client.Client.RemoteEndPoint?.ToString();
        _logger.LogInformation("Client connected: {ClientEndpoint}", clientEndpoint);
        
        try
        {
            using (client)
            {
                var stream = client.GetStream();
                var buffer = new byte[1024];
                
                while (!cancellationToken.IsCancellationRequested && client.Connected)
                {
                    var bytesRead = await stream.ReadAsync(buffer, cancellationToken);
                    if (bytesRead == 0)
                        break;
                    
                    var request = new byte[bytesRead];
                    Array.Copy(buffer, 0, request, 0, bytesRead);
                    
                    var response = ProcessModbusRequest(request);
                    if (response != null)
                    {
                        await stream.WriteAsync(response, cancellationToken);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling client {ClientEndpoint}", clientEndpoint);
        }
        finally
        {
            _logger.LogInformation("Client disconnected: {ClientEndpoint}", clientEndpoint);
        }
    }

    private byte[]? ProcessModbusRequest(byte[] request)
    {
        try
        {
            if (request.Length < 8) // Minimum MBAP header + function code + data
                return null;
            
            // Parse MBAP header
            var transactionId = (ushort)((request[0] << 8) | request[1]);
            var protocolId = (ushort)((request[2] << 8) | request[3]);
            var length = (ushort)((request[4] << 8) | request[5]);
            var unitId = request[6];
            var functionCode = request[7];
            
            _logger.LogDebug("Received request: TxId={TransactionId}, Function={FunctionCode}, Unit={UnitId}", 
                transactionId, functionCode, unitId);
            
            if (protocolId != 0) // Must be 0 for Modbus TCP
                return null;
            
            return functionCode switch
            {
                0x01 => ProcessReadCoils(request, transactionId, unitId),
                0x02 => ProcessReadDiscreteInputs(request, transactionId, unitId),
                0x03 => ProcessReadHoldingRegisters(request, transactionId, unitId),
                0x04 => ProcessReadInputRegisters(request, transactionId, unitId),
                0x05 => ProcessWriteSingleCoil(request, transactionId, unitId),
                0x06 => ProcessWriteSingleRegister(request, transactionId, unitId),
                0x0F => ProcessWriteMultipleCoils(request, transactionId, unitId),
                0x10 => ProcessWriteMultipleRegisters(request, transactionId, unitId),
                _ => CreateErrorResponse(transactionId, unitId, functionCode, 0x01) // Illegal function
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Modbus request");
            return null;
        }
    }

    private byte[] ProcessReadCoils(byte[] request, ushort transactionId, byte unitId)
    {
        var startAddress = (ushort)((request[8] << 8) | request[9]);
        var count = (ushort)((request[10] << 8) | request[11]);
        
        if (startAddress + count > _coils.Length)
            return CreateErrorResponse(transactionId, unitId, 0x01, 0x02); // Illegal data address
        
        var byteCount = (byte)((count + 7) / 8);
        var data = new byte[byteCount];
        
        for (int i = 0; i < count; i++)
        {
            if (_coils[startAddress + i])
            {
                var byteIndex = i / 8;
                var bitIndex = i % 8;
                data[byteIndex] |= (byte)(1 << bitIndex);
            }
        }
        
        return CreateResponse(transactionId, unitId, 0x01, new byte[] { byteCount }.Concat(data).ToArray());
    }

    private byte[] ProcessReadHoldingRegisters(byte[] request, ushort transactionId, byte unitId)
    {
        var startAddress = (ushort)((request[8] << 8) | request[9]);
        var count = (ushort)((request[10] << 8) | request[11]);
        
        if (startAddress + count > _holdingRegisters.Length)
            return CreateErrorResponse(transactionId, unitId, 0x03, 0x02); // Illegal data address
        
        var byteCount = (byte)(count * 2);
        var data = new byte[1 + byteCount];
        data[0] = byteCount;
        
        for (int i = 0; i < count; i++)
        {
            var value = _holdingRegisters[startAddress + i];
            data[1 + i * 2] = (byte)(value >> 8);
            data[2 + i * 2] = (byte)(value & 0xFF);
        }
        
        return CreateResponse(transactionId, unitId, 0x03, data);
    }

    private byte[] ProcessWriteSingleCoil(byte[] request, ushort transactionId, byte unitId)
    {
        var address = (ushort)((request[8] << 8) | request[9]);
        var value = ((request[10] << 8) | request[11]) == 0xFF00;
        
        if (address >= _coils.Length)
            return CreateErrorResponse(transactionId, unitId, 0x05, 0x02); // Illegal data address
        
        _coils[address] = value;
        
        // Echo back the request
        var responseData = new byte[4];
        responseData[0] = request[8]; // Address high
        responseData[1] = request[9]; // Address low
        responseData[2] = request[10]; // Value high
        responseData[3] = request[11]; // Value low
        
        return CreateResponse(transactionId, unitId, 0x05, responseData);
    }

    private byte[] ProcessWriteSingleRegister(byte[] request, ushort transactionId, byte unitId)
    {
        var address = (ushort)((request[8] << 8) | request[9]);
        var value = (ushort)((request[10] << 8) | request[11]);
        
        if (address >= _holdingRegisters.Length)
            return CreateErrorResponse(transactionId, unitId, 0x06, 0x02); // Illegal data address
        
        _holdingRegisters[address] = value;
        
        // Echo back the request
        var responseData = new byte[4];
        responseData[0] = request[8]; // Address high
        responseData[1] = request[9]; // Address low
        responseData[2] = request[10]; // Value high
        responseData[3] = request[11]; // Value low
        
        return CreateResponse(transactionId, unitId, 0x06, responseData);
    }

    // Simplified implementations for other functions
    private byte[] ProcessReadDiscreteInputs(byte[] request, ushort transactionId, byte unitId)
    {
        // Similar to read coils but from discrete inputs
        return ProcessReadCoils(request, transactionId, unitId); // Simplified
    }

    private byte[] ProcessReadInputRegisters(byte[] request, ushort transactionId, byte unitId)
    {
        // Similar to read holding registers but from input registers
        return ProcessReadHoldingRegisters(request, transactionId, unitId); // Simplified
    }

    private byte[] ProcessWriteMultipleCoils(byte[] request, ushort transactionId, byte unitId)
    {
        // Simplified implementation
        return CreateResponse(transactionId, unitId, 0x0F, new byte[] { request[8], request[9], request[10], request[11] });
    }

    private byte[] ProcessWriteMultipleRegisters(byte[] request, ushort transactionId, byte unitId)
    {
        // Simplified implementation
        return CreateResponse(transactionId, unitId, 0x10, new byte[] { request[8], request[9], request[10], request[11] });
    }

    private static byte[] CreateResponse(ushort transactionId, byte unitId, byte functionCode, byte[] data)
    {
        var length = (ushort)(data.Length + 2); // Unit ID + Function Code + Data
        var response = new byte[6 + length];
        
        // MBAP Header
        response[0] = (byte)(transactionId >> 8);
        response[1] = (byte)(transactionId & 0xFF);
        response[2] = 0x00; // Protocol ID
        response[3] = 0x00;
        response[4] = (byte)(length >> 8);
        response[5] = (byte)(length & 0xFF);
        
        // PDU
        response[6] = unitId;
        response[7] = functionCode;
        Array.Copy(data, 0, response, 8, data.Length);
        
        return response;
    }

    private static byte[] CreateErrorResponse(ushort transactionId, byte unitId, byte functionCode, byte exceptionCode)
    {
        return CreateResponse(transactionId, unitId, (byte)(functionCode | 0x80), new byte[] { exceptionCode });
    }
}
