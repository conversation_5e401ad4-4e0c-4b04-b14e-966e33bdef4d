using System.Collections.Concurrent;
using System.Net.Sockets;
using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Abstractions;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Configuration;
using Ngp.Communication.ModbusTcpMaster.Connection;
using Ngp.Communication.ModbusTcpMaster.Protocol;
using Ngp.Communication.ModbusTcpMaster.Events;

namespace Ngp.Communication.ModbusTcpMaster.Engine;

/// <summary>
/// Main Modbus TCP Master engine implementation
/// </summary>
public class ModbusMasterEngine : IModbusMaster
{
    private readonly ILogger<ModbusMasterEngine> _logger;
    private readonly ModbusConfiguration _configuration;
    private readonly IModbusConnectionManager _connectionManager;
    private readonly ModbusEventDispatcher _eventDispatcher;
    private readonly RegisterOptimizer _registerOptimizer;
    private readonly ModbusTcpProtocol _tcpProtocol;
    private readonly ModbusRtuOverTcpProtocol _rtuOverTcpProtocol;
    private readonly ConcurrentDictionary<string, PollingContext> _pollingContexts;
    private readonly SemaphoreSlim _requestSemaphore;
    private int _transactionId;
    private bool _disposed;

    /// <summary>
    /// Initializes a new instance of ModbusMasterEngine
    /// </summary>
    /// <param name="configuration">Configuration options</param>
    /// <param name="connectionManager">Connection manager</param>
    /// <param name="eventDispatcher">Event dispatcher</param>
    /// <param name="logger">Logger instance</param>
    public ModbusMasterEngine(
        ModbusConfiguration configuration,
        IModbusConnectionManager connectionManager,
        ModbusEventDispatcher eventDispatcher,
        ILogger<ModbusMasterEngine> logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        _eventDispatcher = eventDispatcher ?? throw new ArgumentNullException(nameof(eventDispatcher));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _registerOptimizer = new RegisterOptimizer(_configuration.RegisterOptimization);
        _tcpProtocol = new ModbusTcpProtocol();
        _rtuOverTcpProtocol = new ModbusRtuOverTcpProtocol();
        _pollingContexts = new ConcurrentDictionary<string, PollingContext>();
        _requestSemaphore = new SemaphoreSlim(1000, 1000); // Limit concurrent requests

        // Subscribe to event dispatcher events
        _eventDispatcher.ConnectionStatusChanged += OnConnectionStatusChanged;
        _eventDispatcher.DataUpdated += OnDataUpdated;
        _eventDispatcher.ErrorOccurred += OnErrorOccurred;

        _logger.LogInformation("ModbusMasterEngine initialized");
    }

    /// <summary>
    /// Event triggered when connection status changes
    /// </summary>
    public event EventHandler<ConnectionStatusEventArgs>? ConnectionStatusChanged;

    /// <summary>
    /// Event triggered when data is updated
    /// </summary>
    public event EventHandler<DataUpdatedEventArgs>? DataUpdated;

    /// <summary>
    /// Event triggered when an error occurs
    /// </summary>
    public event EventHandler<ModbusErrorEventArgs>? ErrorOccurred;

    /// <summary>
    /// Gets the current connection status for a specific endpoint
    /// </summary>
    /// <param name="endpoint">The endpoint to check</param>
    /// <returns>Connection status</returns>
    public ConnectionStatus GetConnectionStatus(ModbusEndpoint endpoint)
    {
        if (_disposed)
        {
            return ConnectionStatus.Disconnected;
        }

        return _connectionManager.GetConnectionStatus(endpoint);
    }

    /// <summary>
    /// Connects to a Modbus slave device
    /// </summary>
    /// <param name="endpoint">The endpoint to connect to</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the connection operation</returns>
    public async Task<bool> ConnectAsync(ModbusEndpoint endpoint, CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(ModbusMasterEngine));
        }

        try
        {
            var connection = await _connectionManager.GetConnectionAsync(endpoint, cancellationToken);
            return await connection.ConnectAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to connect to {Endpoint}", endpoint);
            
            _eventDispatcher.DispatchError(new ModbusErrorEventArgs
            {
                Endpoint = endpoint,
                ErrorType = ModbusErrorType.ConnectionError,
                Message = $"Failed to connect to {endpoint}",
                Exception = ex
            });

            return false;
        }
    }

    /// <summary>
    /// Disconnects from a Modbus slave device
    /// </summary>
    /// <param name="endpoint">The endpoint to disconnect from</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the disconnection operation</returns>
    public async Task DisconnectAsync(ModbusEndpoint endpoint, CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            return;
        }

        try
        {
            // Stop polling for this endpoint
            var pollingKey = GetPollingKey(endpoint);
            if (_pollingContexts.TryRemove(pollingKey, out var pollingContext))
            {
                pollingContext.CancellationTokenSource.Cancel();
            }

            // Disconnect the connection
            await _connectionManager.RemoveConnectionAsync(endpoint, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disconnecting from {Endpoint}", endpoint);
        }
    }

    /// <summary>
    /// Reads coils from a Modbus slave
    /// </summary>
    /// <param name="request">The read request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Read response</returns>
    public async Task<ModbusResponse<bool[]>> ReadCoilsAsync(ModbusReadRequest request, CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(ModbusMasterEngine));
        }

        var readRequest = request with { RegisterType = ModbusRegisterType.Coil };
        return await ExecuteReadRequestAsync<bool[]>(readRequest, cancellationToken);
    }

    /// <summary>
    /// Reads discrete inputs from a Modbus slave
    /// </summary>
    /// <param name="request">The read request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Read response</returns>
    public async Task<ModbusResponse<bool[]>> ReadDiscreteInputsAsync(ModbusReadRequest request, CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(ModbusMasterEngine));
        }

        var readRequest = request with { RegisterType = ModbusRegisterType.DiscreteInput };
        return await ExecuteReadRequestAsync<bool[]>(readRequest, cancellationToken);
    }

    /// <summary>
    /// Reads holding registers from a Modbus slave
    /// </summary>
    /// <param name="request">The read request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Read response</returns>
    public async Task<ModbusResponse<ushort[]>> ReadHoldingRegistersAsync(ModbusReadRequest request, CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(ModbusMasterEngine));
        }

        var readRequest = request with { RegisterType = ModbusRegisterType.HoldingRegister };
        return await ExecuteReadRequestAsync<ushort[]>(readRequest, cancellationToken);
    }

    /// <summary>
    /// Reads input registers from a Modbus slave
    /// </summary>
    /// <param name="request">The read request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Read response</returns>
    public async Task<ModbusResponse<ushort[]>> ReadInputRegistersAsync(ModbusReadRequest request, CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(ModbusMasterEngine));
        }

        var readRequest = request with { RegisterType = ModbusRegisterType.InputRegister };
        return await ExecuteReadRequestAsync<ushort[]>(readRequest, cancellationToken);
    }

    /// <summary>
    /// Writes a single coil to a Modbus slave
    /// </summary>
    /// <param name="request">The write request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Write response</returns>
    public async Task<ModbusResponse> WriteSingleCoilAsync(ModbusWriteSingleRequest<bool> request, CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(ModbusMasterEngine));
        }

        var writeRequest = request with { RegisterType = ModbusRegisterType.Coil };
        return await ExecuteWriteSingleRequestAsync(writeRequest, cancellationToken);
    }

    /// <summary>
    /// Writes a single holding register to a Modbus slave
    /// </summary>
    /// <param name="request">The write request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Write response</returns>
    public async Task<ModbusResponse> WriteSingleRegisterAsync(ModbusWriteSingleRequest<ushort> request, CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(ModbusMasterEngine));
        }

        var writeRequest = request with { RegisterType = ModbusRegisterType.HoldingRegister };
        return await ExecuteWriteSingleRequestAsync(writeRequest, cancellationToken);
    }

    /// <summary>
    /// Writes multiple coils to a Modbus slave
    /// </summary>
    /// <param name="request">The write request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Write response</returns>
    public async Task<ModbusResponse> WriteMultipleCoilsAsync(ModbusWriteMultipleRequest<bool> request, CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(ModbusMasterEngine));
        }

        var writeRequest = request with { RegisterType = ModbusRegisterType.Coil };
        return await ExecuteWriteMultipleRequestAsync(writeRequest, cancellationToken);
    }

    /// <summary>
    /// Writes multiple holding registers to a Modbus slave
    /// </summary>
    /// <param name="request">The write request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Write response</returns>
    public async Task<ModbusResponse> WriteMultipleRegistersAsync(ModbusWriteMultipleRequest<ushort> request, CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(ModbusMasterEngine));
        }

        var writeRequest = request with { RegisterType = ModbusRegisterType.HoldingRegister };
        return await ExecuteWriteMultipleRequestAsync(writeRequest, cancellationToken);
    }

    /// <summary>
    /// Starts continuous polling for specified register ranges
    /// </summary>
    /// <param name="endpoint">The endpoint to poll</param>
    /// <param name="registerRanges">The register ranges to poll</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the polling operation</returns>
    public Task StartPollingAsync(ModbusEndpoint endpoint, IEnumerable<RegisterRange> registerRanges, CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(ModbusMasterEngine));
        }

        var pollingKey = GetPollingKey(endpoint);

        // Stop existing polling if any
        if (_pollingContexts.TryRemove(pollingKey, out var existingContext))
        {
            existingContext.CancellationTokenSource.Cancel();
        }

        // Create new polling context
        var pollingContext = new PollingContext
        {
            Endpoint = endpoint,
            RegisterRanges = registerRanges.ToList(),
            CancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken)
        };

        _pollingContexts[pollingKey] = pollingContext;

        // Start polling task
        _ = Task.Run(() => PollingLoop(pollingContext), pollingContext.CancellationTokenSource.Token);

        _logger.LogInformation("Started polling for {Endpoint} with {RangeCount} register ranges", endpoint, pollingContext.RegisterRanges.Count);

        return Task.CompletedTask;
    }

    /// <summary>
    /// Stops continuous polling for an endpoint
    /// </summary>
    /// <param name="endpoint">The endpoint to stop polling</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the stop operation</returns>
    public Task StopPollingAsync(ModbusEndpoint endpoint, CancellationToken cancellationToken = default)
    {
        if (_disposed)
        {
            return Task.CompletedTask;
        }

        var pollingKey = GetPollingKey(endpoint);
        if (_pollingContexts.TryRemove(pollingKey, out var pollingContext))
        {
            pollingContext.CancellationTokenSource.Cancel();
            _logger.LogInformation("Stopped polling for {Endpoint}", endpoint);
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Disposes the engine and releases resources
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
        {
            return;
        }

        _disposed = true;

        try
        {
            // Stop all polling
            foreach (var pollingContext in _pollingContexts.Values)
            {
                pollingContext.CancellationTokenSource.Cancel();
            }
            _pollingContexts.Clear();

            // Dispose components
            _connectionManager.Dispose();
            _eventDispatcher.Dispose();
            _requestSemaphore.Dispose();

            _logger.LogInformation("ModbusMasterEngine disposed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during ModbusMasterEngine disposal");
        }
    }

    /// <summary>
    /// Gets a unique key for polling context
    /// </summary>
    /// <param name="endpoint">The endpoint</param>
    /// <returns>Polling key</returns>
    private static string GetPollingKey(ModbusEndpoint endpoint)
    {
        return $"{endpoint.IpAddress}:{endpoint.Port}";
    }

    /// <summary>
    /// Gets the next transaction ID
    /// </summary>
    /// <returns>Transaction ID</returns>
    private ushort GetNextTransactionId()
    {
        return (ushort)(Interlocked.Increment(ref _transactionId) & 0xFFFF);
    }

    /// <summary>
    /// Event handler for connection status changes
    /// </summary>
    private void OnConnectionStatusChanged(object? sender, ConnectionStatusEventArgs e)
    {
        ConnectionStatusChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Event handler for data updates
    /// </summary>
    private void OnDataUpdated(object? sender, DataUpdatedEventArgs e)
    {
        DataUpdated?.Invoke(this, e);
    }

    /// <summary>
    /// Event handler for errors
    /// </summary>
    private void OnErrorOccurred(object? sender, ModbusErrorEventArgs e)
    {
        ErrorOccurred?.Invoke(this, e);
    }

    /// <summary>
    /// Executes a read request
    /// </summary>
    /// <typeparam name="T">Type of data to read</typeparam>
    /// <param name="request">The read request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Read response</returns>
    private async Task<ModbusResponse<T>> ExecuteReadRequestAsync<T>(ModbusReadRequest request, CancellationToken cancellationToken)
    {
        var startTime = DateTime.UtcNow;

        await _requestSemaphore.WaitAsync(cancellationToken);
        try
        {
            var connection = await _connectionManager.GetConnectionAsync(request.Endpoint, cancellationToken);
            if (!connection.IsConnected)
            {
                await connection.ConnectAsync(cancellationToken);
            }

            var protocol = GetProtocol(request.ProtocolType);
            var transactionId = GetNextTransactionId();
            var requestBytes = protocol.CreateReadRequest(request, transactionId);

            var responseBytes = await connection.SendRequestAsync(requestBytes, cancellationToken);
            var responseData = protocol.ParseResponse(responseBytes, transactionId);

            if (responseData.IsError)
            {
                return new ModbusResponse<T>
                {
                    IsSuccess = false,
                    ErrorMessage = ModbusExceptionCodes.GetDescription(responseData.ExceptionCode ?? 0),
                    ExceptionCode = responseData.ExceptionCode,
                    RoundTripTime = DateTime.UtcNow - startTime
                };
            }

            var data = ParseReadResponseData<T>(responseData, request.RegisterType, request.Count);

            return new ModbusResponse<T>
            {
                IsSuccess = true,
                Data = data,
                RoundTripTime = DateTime.UtcNow - startTime
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing read request for {Endpoint}", request.Endpoint);

            _eventDispatcher.DispatchError(new ModbusErrorEventArgs
            {
                Endpoint = request.Endpoint,
                SlaveId = request.SlaveId,
                ErrorType = GetErrorType(ex),
                Message = ex.Message,
                Exception = ex
            });

            return new ModbusResponse<T>
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                RoundTripTime = DateTime.UtcNow - startTime
            };
        }
        finally
        {
            _requestSemaphore.Release();
        }
    }

    /// <summary>
    /// Executes a single write request
    /// </summary>
    /// <typeparam name="T">Type of data to write</typeparam>
    /// <param name="request">The write request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Write response</returns>
    private async Task<ModbusResponse> ExecuteWriteSingleRequestAsync<T>(ModbusWriteSingleRequest<T> request, CancellationToken cancellationToken)
    {
        var startTime = DateTime.UtcNow;

        await _requestSemaphore.WaitAsync(cancellationToken);
        try
        {
            var connection = await _connectionManager.GetConnectionAsync(request.Endpoint, cancellationToken);
            if (!connection.IsConnected)
            {
                await connection.ConnectAsync(cancellationToken);
            }

            var protocol = GetProtocol(request.ProtocolType);
            var transactionId = GetNextTransactionId();
            var requestBytes = protocol.CreateWriteSingleRequest(request, transactionId);

            var responseBytes = await connection.SendRequestAsync(requestBytes, cancellationToken);
            var responseData = protocol.ParseResponse(responseBytes, transactionId);

            if (responseData.IsError)
            {
                return new ModbusResponse
                {
                    IsSuccess = false,
                    ErrorMessage = ModbusExceptionCodes.GetDescription(responseData.ExceptionCode ?? 0),
                    ExceptionCode = responseData.ExceptionCode,
                    RoundTripTime = DateTime.UtcNow - startTime
                };
            }

            return new ModbusResponse
            {
                IsSuccess = true,
                RoundTripTime = DateTime.UtcNow - startTime
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing write single request for {Endpoint}", request.Endpoint);

            _eventDispatcher.DispatchError(new ModbusErrorEventArgs
            {
                Endpoint = request.Endpoint,
                SlaveId = request.SlaveId,
                ErrorType = GetErrorType(ex),
                Message = ex.Message,
                Exception = ex
            });

            return new ModbusResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                RoundTripTime = DateTime.UtcNow - startTime
            };
        }
        finally
        {
            _requestSemaphore.Release();
        }
    }

    /// <summary>
    /// Executes a multiple write request
    /// </summary>
    /// <typeparam name="T">Type of data to write</typeparam>
    /// <param name="request">The write request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Write response</returns>
    private async Task<ModbusResponse> ExecuteWriteMultipleRequestAsync<T>(ModbusWriteMultipleRequest<T> request, CancellationToken cancellationToken)
    {
        var startTime = DateTime.UtcNow;

        await _requestSemaphore.WaitAsync(cancellationToken);
        try
        {
            var connection = await _connectionManager.GetConnectionAsync(request.Endpoint, cancellationToken);
            if (!connection.IsConnected)
            {
                await connection.ConnectAsync(cancellationToken);
            }

            var protocol = GetProtocol(request.ProtocolType);
            var transactionId = GetNextTransactionId();
            var requestBytes = protocol.CreateWriteMultipleRequest(request, transactionId);

            var responseBytes = await connection.SendRequestAsync(requestBytes, cancellationToken);
            var responseData = protocol.ParseResponse(responseBytes, transactionId);

            if (responseData.IsError)
            {
                return new ModbusResponse
                {
                    IsSuccess = false,
                    ErrorMessage = ModbusExceptionCodes.GetDescription(responseData.ExceptionCode ?? 0),
                    ExceptionCode = responseData.ExceptionCode,
                    RoundTripTime = DateTime.UtcNow - startTime
                };
            }

            return new ModbusResponse
            {
                IsSuccess = true,
                RoundTripTime = DateTime.UtcNow - startTime
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing write multiple request for {Endpoint}", request.Endpoint);

            _eventDispatcher.DispatchError(new ModbusErrorEventArgs
            {
                Endpoint = request.Endpoint,
                SlaveId = request.SlaveId,
                ErrorType = GetErrorType(ex),
                Message = ex.Message,
                Exception = ex
            });

            return new ModbusResponse
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                RoundTripTime = DateTime.UtcNow - startTime
            };
        }
        finally
        {
            _requestSemaphore.Release();
        }
    }

    /// <summary>
    /// Gets the appropriate protocol implementation
    /// </summary>
    /// <param name="protocolType">Protocol type</param>
    /// <returns>Protocol implementation</returns>
    private ModbusProtocol GetProtocol(ModbusProtocolType protocolType)
    {
        return protocolType switch
        {
            ModbusProtocolType.ModbusTcp => _tcpProtocol,
            ModbusProtocolType.ModbusRtuOverTcp => _rtuOverTcpProtocol,
            _ => throw new ArgumentException($"Unsupported protocol type: {protocolType}")
        };
    }

    /// <summary>
    /// Parses read response data based on register type
    /// </summary>
    /// <typeparam name="T">Type of data to parse</typeparam>
    /// <param name="responseData">Response data</param>
    /// <param name="registerType">Register type</param>
    /// <param name="count">Expected count</param>
    /// <returns>Parsed data</returns>
    private static T ParseReadResponseData<T>(ModbusResponseData responseData, ModbusRegisterType registerType, ushort count)
    {
        if (responseData.Data == null)
        {
            throw new InvalidOperationException("Response data is null");
        }

        return registerType switch
        {
            ModbusRegisterType.Coil or ModbusRegisterType.DiscreteInput when typeof(T) == typeof(bool[]) =>
                (T)(object)ModbusProtocol.UnpackBooleans(responseData.Data.Skip(1).ToArray(), count),

            ModbusRegisterType.HoldingRegister or ModbusRegisterType.InputRegister when typeof(T) == typeof(ushort[]) =>
                (T)(object)ParseRegisterData(responseData.Data.Skip(1).ToArray()),

            _ => throw new ArgumentException($"Invalid register type {registerType} for data type {typeof(T)}")
        };
    }

    /// <summary>
    /// Parses register data from bytes
    /// </summary>
    /// <param name="data">Data bytes</param>
    /// <returns>Register values</returns>
    private static ushort[] ParseRegisterData(byte[] data)
    {
        var registers = new ushort[data.Length / 2];
        for (int i = 0; i < registers.Length; i++)
        {
            registers[i] = (ushort)((data[i * 2] << 8) | data[i * 2 + 1]);
        }
        return registers;
    }

    /// <summary>
    /// Gets error type from exception
    /// </summary>
    /// <param name="exception">Exception</param>
    /// <returns>Error type</returns>
    private static ModbusErrorType GetErrorType(Exception exception)
    {
        return exception switch
        {
            TimeoutException => ModbusErrorType.TimeoutError,
            SocketException => ModbusErrorType.NetworkError,
            InvalidOperationException when exception.Message.Contains("CRC") => ModbusErrorType.CrcError,
            _ => ModbusErrorType.UnknownError
        };
    }

    /// <summary>
    /// Polling loop for continuous data acquisition
    /// </summary>
    /// <param name="pollingContext">Polling context</param>
    private async Task PollingLoop(PollingContext pollingContext)
    {
        try
        {
            while (!pollingContext.CancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    await PerformPollingCycle(pollingContext);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in polling cycle for {Endpoint}", pollingContext.Endpoint);

                    _eventDispatcher.DispatchError(new ModbusErrorEventArgs
                    {
                        Endpoint = pollingContext.Endpoint,
                        ErrorType = GetErrorType(ex),
                        Message = $"Polling error: {ex.Message}",
                        Exception = ex
                    });
                }

                // Wait before next cycle
                var minInterval = pollingContext.RegisterRanges.Min(r => r.PollingIntervalMs);
                await Task.Delay(minInterval, pollingContext.CancellationTokenSource.Token);
            }
        }
        catch (OperationCanceledException)
        {
            // Expected when polling is stopped
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in polling loop for {Endpoint}", pollingContext.Endpoint);
        }
    }

    /// <summary>
    /// Performs a single polling cycle
    /// </summary>
    /// <param name="pollingContext">Polling context</param>
    private async Task PerformPollingCycle(PollingContext pollingContext)
    {
        var optimizedRequests = _registerOptimizer.CreateOptimizedReadRequests(
            pollingContext.Endpoint,
            pollingContext.RegisterRanges);

        var tasks = new List<Task>();

        foreach (var request in optimizedRequests)
        {
            if (_configuration.EnableParallelProcessing)
            {
                tasks.Add(ProcessPollingRequest(request));
            }
            else
            {
                await ProcessPollingRequest(request);
                await Task.Delay(_configuration.RequestGapMs);
            }
        }

        if (_configuration.EnableParallelProcessing && tasks.Count > 0)
        {
            await Task.WhenAll(tasks);
        }
    }

    /// <summary>
    /// Processes a single polling request
    /// </summary>
    /// <param name="request">Read request</param>
    private async Task ProcessPollingRequest(ModbusReadRequest request)
    {
        try
        {
            object? data = null;

            switch (request.RegisterType)
            {
                case ModbusRegisterType.Coil:
                    var coilResponse = await ReadCoilsAsync(request);
                    if (coilResponse.IsSuccess)
                    {
                        data = coilResponse.Data;
                    }
                    break;

                case ModbusRegisterType.DiscreteInput:
                    var discreteResponse = await ReadDiscreteInputsAsync(request);
                    if (discreteResponse.IsSuccess)
                    {
                        data = discreteResponse.Data;
                    }
                    break;

                case ModbusRegisterType.HoldingRegister:
                    var holdingResponse = await ReadHoldingRegistersAsync(request);
                    if (holdingResponse.IsSuccess)
                    {
                        data = holdingResponse.Data;
                    }
                    break;

                case ModbusRegisterType.InputRegister:
                    var inputResponse = await ReadInputRegistersAsync(request);
                    if (inputResponse.IsSuccess)
                    {
                        data = inputResponse.Data;
                    }
                    break;
            }

            if (data != null)
            {
                _eventDispatcher.DispatchDataUpdated(new DataUpdatedEventArgs
                {
                    Endpoint = request.Endpoint,
                    SlaveId = request.SlaveId,
                    RegisterType = request.RegisterType,
                    StartAddress = request.StartAddress,
                    Data = data
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing polling request for {Endpoint}", request.Endpoint);
        }
    }

    /// <summary>
    /// Represents a polling context for an endpoint
    /// </summary>
    private class PollingContext
    {
        public required ModbusEndpoint Endpoint { get; init; }
        public required List<RegisterRange> RegisterRanges { get; init; }
        public required CancellationTokenSource CancellationTokenSource { get; init; }
    }
}
