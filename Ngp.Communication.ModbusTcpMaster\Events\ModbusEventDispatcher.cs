using System.Threading.Channels;
using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.Configuration;

namespace Ngp.Communication.ModbusTcpMaster.Events;

/// <summary>
/// High-performance event dispatcher for Modbus events using channels
/// </summary>
public class ModbusEventDispatcher : IDisposable
{
    private readonly ILogger<ModbusEventDispatcher> _logger;
    private readonly EventProcessingConfiguration _configuration;
    private readonly Channel<EventItem> _eventChannel;
    private readonly ChannelWriter<EventItem> _eventWriter;
    private readonly ChannelReader<EventItem> _eventReader;
    private readonly Task[] _processingTasks;
    private readonly CancellationTokenSource _cancellationTokenSource;
    private bool _disposed;

    /// <summary>
    /// Initializes a new instance of ModbusEventDispatcher
    /// </summary>
    /// <param name="configuration">Event processing configuration</param>
    /// <param name="logger">Logger instance</param>
    public ModbusEventDispatcher(
        EventProcessingConfiguration configuration,
        ILogger<ModbusEventDispatcher> logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _cancellationTokenSource = new CancellationTokenSource();

        // Create bounded channel for event processing
        var channelOptions = new BoundedChannelOptions(_configuration.MaxEventQueueSize)
        {
            FullMode = BoundedChannelFullMode.DropOldest,
            SingleReader = false,
            SingleWriter = false,
            AllowSynchronousContinuations = false
        };

        _eventChannel = Channel.CreateBounded<EventItem>(channelOptions);
        _eventWriter = _eventChannel.Writer;
        _eventReader = _eventChannel.Reader;

        // Start processing tasks
        _processingTasks = new Task[_configuration.EventProcessorThreads];
        for (int i = 0; i < _configuration.EventProcessorThreads; i++)
        {
            _processingTasks[i] = Task.Run(() => ProcessEventsAsync(_cancellationTokenSource.Token), _cancellationTokenSource.Token);
        }

        _logger.LogInformation("ModbusEventDispatcher initialized with {ThreadCount} processing threads and queue size {QueueSize}",
            _configuration.EventProcessorThreads, _configuration.MaxEventQueueSize);
    }

    /// <summary>
    /// Event triggered when connection status changes
    /// </summary>
    public event EventHandler<ConnectionStatusEventArgs>? ConnectionStatusChanged;

    /// <summary>
    /// Event triggered when data is updated
    /// </summary>
    public event EventHandler<DataUpdatedEventArgs>? DataUpdated;

    /// <summary>
    /// Event triggered when an error occurs
    /// </summary>
    public event EventHandler<ModbusErrorEventArgs>? ErrorOccurred;

    /// <summary>
    /// Dispatches a connection status change event
    /// </summary>
    /// <param name="eventArgs">Event arguments</param>
    public void DispatchConnectionStatusChanged(ConnectionStatusEventArgs eventArgs)
    {
        if (_disposed)
        {
            return;
        }

        var eventItem = new EventItem
        {
            EventType = EventType.ConnectionStatusChanged,
            EventArgs = eventArgs,
            Timestamp = DateTime.UtcNow
        };

        if (!_eventWriter.TryWrite(eventItem))
        {
            _logger.LogWarning("Failed to queue connection status change event - queue is full");
        }
    }

    /// <summary>
    /// Dispatches a data updated event
    /// </summary>
    /// <param name="eventArgs">Event arguments</param>
    public void DispatchDataUpdated(DataUpdatedEventArgs eventArgs)
    {
        if (_disposed)
        {
            return;
        }

        var eventItem = new EventItem
        {
            EventType = EventType.DataUpdated,
            EventArgs = eventArgs,
            Timestamp = DateTime.UtcNow
        };

        if (!_eventWriter.TryWrite(eventItem))
        {
            _logger.LogWarning("Failed to queue data updated event - queue is full");
        }
    }

    /// <summary>
    /// Dispatches an error event
    /// </summary>
    /// <param name="eventArgs">Event arguments</param>
    public void DispatchError(ModbusErrorEventArgs eventArgs)
    {
        if (_disposed)
        {
            return;
        }

        var eventItem = new EventItem
        {
            EventType = EventType.Error,
            EventArgs = eventArgs,
            Timestamp = DateTime.UtcNow
        };

        if (!_eventWriter.TryWrite(eventItem))
        {
            _logger.LogWarning("Failed to queue error event - queue is full");
        }
    }

    /// <summary>
    /// Disposes the event dispatcher and releases resources
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
        {
            return;
        }

        _disposed = true;

        try
        {
            // Signal completion and wait for processing tasks to finish
            _eventWriter.Complete();
            _cancellationTokenSource.Cancel();

            Task.WaitAll(_processingTasks, TimeSpan.FromSeconds(5));

            _cancellationTokenSource.Dispose();

            _logger.LogInformation("ModbusEventDispatcher disposed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during ModbusEventDispatcher disposal");
        }
    }

    /// <summary>
    /// Processes events from the channel
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    private async Task ProcessEventsAsync(CancellationToken cancellationToken)
    {
        try
        {
            await foreach (var eventItem in _eventReader.ReadAllAsync(cancellationToken))
            {
                try
                {
                    await ProcessEventItemAsync(eventItem, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing event of type {EventType}", eventItem.EventType);
                }
            }
        }
        catch (OperationCanceledException)
        {
            // Expected when cancellation is requested
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in event processing loop");
        }
    }

    /// <summary>
    /// Processes a single event item
    /// </summary>
    /// <param name="eventItem">The event item to process</param>
    /// <param name="cancellationToken">Cancellation token</param>
    private async Task ProcessEventItemAsync(EventItem eventItem, CancellationToken cancellationToken)
    {
        using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
        timeoutCts.CancelAfter(_configuration.EventProcessingTimeoutMs);

        try
        {
            if (_configuration.EnableParallelEventProcessing)
            {
                // Process events in parallel for better performance
                await Task.Run(() => InvokeEventHandlers(eventItem), timeoutCts.Token);
            }
            else
            {
                InvokeEventHandlers(eventItem);
            }
        }
        catch (OperationCanceledException) when (timeoutCts.Token.IsCancellationRequested)
        {
            _logger.LogWarning("Event processing timed out for event type {EventType}", eventItem.EventType);
        }
    }

    /// <summary>
    /// Invokes the appropriate event handlers for an event item
    /// </summary>
    /// <param name="eventItem">The event item</param>
    private void InvokeEventHandlers(EventItem eventItem)
    {
        try
        {
            switch (eventItem.EventType)
            {
                case EventType.ConnectionStatusChanged:
                    if (eventItem.EventArgs is ConnectionStatusEventArgs connectionArgs)
                    {
                        ConnectionStatusChanged?.Invoke(this, connectionArgs);
                    }
                    break;

                case EventType.DataUpdated:
                    if (eventItem.EventArgs is DataUpdatedEventArgs dataArgs)
                    {
                        DataUpdated?.Invoke(this, dataArgs);
                    }
                    break;

                case EventType.Error:
                    if (eventItem.EventArgs is ModbusErrorEventArgs errorArgs)
                    {
                        ErrorOccurred?.Invoke(this, errorArgs);
                    }
                    break;

                default:
                    _logger.LogWarning("Unknown event type: {EventType}", eventItem.EventType);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invoking event handlers for event type {EventType}", eventItem.EventType);
        }
    }

    /// <summary>
    /// Represents an event item in the processing queue
    /// </summary>
    private class EventItem
    {
        public EventType EventType { get; set; }
        public object? EventArgs { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// Types of events that can be processed
    /// </summary>
    private enum EventType
    {
        ConnectionStatusChanged,
        DataUpdated,
        Error
    }
}
