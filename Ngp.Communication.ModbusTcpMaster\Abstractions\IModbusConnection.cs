using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Events;

namespace Ngp.Communication.ModbusTcpMaster.Abstractions;

/// <summary>
/// Interface for Modbus TCP connection management
/// </summary>
public interface IModbusConnection : IDisposable
{
    /// <summary>
    /// Gets the endpoint this connection is associated with
    /// </summary>
    ModbusEndpoint Endpoint { get; }

    /// <summary>
    /// Gets the current connection status
    /// </summary>
    ConnectionStatus Status { get; }

    /// <summary>
    /// Gets whether the connection is currently connected
    /// </summary>
    bool IsConnected { get; }

    /// <summary>
    /// Gets the last activity timestamp
    /// </summary>
    DateTime LastActivity { get; }

    /// <summary>
    /// Event triggered when connection status changes
    /// </summary>
    event EventHandler<ConnectionStatusEventArgs>? StatusChanged;

    /// <summary>
    /// Connects to the Modbus slave
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if connection successful, false otherwise</returns>
    Task<bool> ConnectAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Disconnects from the Modbus slave
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the disconnection operation</returns>
    Task DisconnectAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Sends a Modbus request and waits for response
    /// </summary>
    /// <param name="request">The request to send</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The response from the slave</returns>
    Task<byte[]> SendRequestAsync(byte[] request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if the connection is healthy
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if connection is healthy, false otherwise</returns>
    Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for managing multiple Modbus connections
/// </summary>
public interface IModbusConnectionManager : IDisposable
{
    /// <summary>
    /// Gets or creates a connection for the specified endpoint
    /// </summary>
    /// <param name="endpoint">The endpoint to get connection for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The connection instance</returns>
    Task<IModbusConnection> GetConnectionAsync(ModbusEndpoint endpoint, CancellationToken cancellationToken = default);

    /// <summary>
    /// Removes and disposes a connection for the specified endpoint
    /// </summary>
    /// <param name="endpoint">The endpoint to remove connection for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the removal operation</returns>
    Task RemoveConnectionAsync(ModbusEndpoint endpoint, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all active connections
    /// </summary>
    /// <returns>Collection of active connections</returns>
    IEnumerable<IModbusConnection> GetActiveConnections();

    /// <summary>
    /// Gets connection status for a specific endpoint
    /// </summary>
    /// <param name="endpoint">The endpoint to check</param>
    /// <returns>Connection status</returns>
    ConnectionStatus GetConnectionStatus(ModbusEndpoint endpoint);
}
