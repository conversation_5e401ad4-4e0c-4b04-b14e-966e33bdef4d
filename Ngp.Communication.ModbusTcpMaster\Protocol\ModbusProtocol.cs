using Ngp.Communication.ModbusTcpMaster.Models;

namespace Ngp.Communication.ModbusTcpMaster.Protocol;

/// <summary>
/// Base class for Modbus protocol implementations
/// </summary>
public abstract class ModbusProtocol
{
    /// <summary>
    /// Creates a read request message
    /// </summary>
    /// <param name="request">The read request</param>
    /// <param name="transactionId">Transaction ID for the request</param>
    /// <returns>The message bytes</returns>
    public abstract byte[] CreateReadRequest(ModbusReadRequest request, ushort transactionId);

    /// <summary>
    /// Creates a single write request message
    /// </summary>
    /// <param name="request">The write request</param>
    /// <param name="transactionId">Transaction ID for the request</param>
    /// <returns>The message bytes</returns>
    public abstract byte[] CreateWriteSingleRequest<T>(ModbusWriteSingleRequest<T> request, ushort transactionId);

    /// <summary>
    /// Creates a multiple write request message
    /// </summary>
    /// <param name="request">The write request</param>
    /// <param name="transactionId">Transaction ID for the request</param>
    /// <returns>The message bytes</returns>
    public abstract byte[] CreateWriteMultipleRequest<T>(ModbusWriteMultipleRequest<T> request, ushort transactionId);

    /// <summary>
    /// Parses a response message
    /// </summary>
    /// <param name="response">The response bytes</param>
    /// <param name="expectedTransactionId">Expected transaction ID</param>
    /// <returns>Parsed response data</returns>
    public abstract ModbusResponseData ParseResponse(byte[] response, ushort expectedTransactionId);

    /// <summary>
    /// Validates if the response is complete
    /// </summary>
    /// <param name="response">The response bytes</param>
    /// <returns>True if complete, false otherwise</returns>
    public abstract bool IsResponseComplete(byte[] response);

    /// <summary>
    /// Gets the expected response length for a request
    /// </summary>
    /// <param name="request">The request</param>
    /// <returns>Expected response length in bytes</returns>
    public abstract int GetExpectedResponseLength(ModbusReadRequest request);

    /// <summary>
    /// Gets the function code for a register type and operation
    /// </summary>
    /// <param name="registerType">The register type</param>
    /// <param name="isWrite">Whether this is a write operation</param>
    /// <param name="isMultiple">Whether this is a multiple operation</param>
    /// <returns>The function code</returns>
    protected static byte GetFunctionCode(ModbusRegisterType registerType, bool isWrite = false, bool isMultiple = false)
    {
        return registerType switch
        {
            ModbusRegisterType.Coil when !isWrite => ModbusFunctionCodes.ReadCoils,
            ModbusRegisterType.Coil when isWrite && !isMultiple => ModbusFunctionCodes.WriteSingleCoil,
            ModbusRegisterType.Coil when isWrite && isMultiple => ModbusFunctionCodes.WriteMultipleCoils,
            ModbusRegisterType.DiscreteInput => ModbusFunctionCodes.ReadDiscreteInputs,
            ModbusRegisterType.HoldingRegister when !isWrite => ModbusFunctionCodes.ReadHoldingRegisters,
            ModbusRegisterType.HoldingRegister when isWrite && !isMultiple => ModbusFunctionCodes.WriteSingleRegister,
            ModbusRegisterType.HoldingRegister when isWrite && isMultiple => ModbusFunctionCodes.WriteMultipleRegisters,
            ModbusRegisterType.InputRegister => ModbusFunctionCodes.ReadInputRegisters,
            _ => throw new ArgumentException($"Invalid register type or operation: {registerType}, Write: {isWrite}, Multiple: {isMultiple}")
        };
    }

    /// <summary>
    /// Converts a 16-bit value to big-endian bytes
    /// </summary>
    /// <param name="value">The value to convert</param>
    /// <returns>Big-endian byte array</returns>
    protected static byte[] ToBigEndianBytes(ushort value)
    {
        return [(byte)(value >> 8), (byte)(value & 0xFF)];
    }

    /// <summary>
    /// Converts big-endian bytes to a 16-bit value
    /// </summary>
    /// <param name="bytes">The bytes to convert</param>
    /// <param name="offset">Offset in the byte array</param>
    /// <returns>The 16-bit value</returns>
    protected static ushort FromBigEndianBytes(byte[] bytes, int offset)
    {
        return (ushort)((bytes[offset] << 8) | bytes[offset + 1]);
    }

    /// <summary>
    /// Converts boolean array to byte array for coil operations
    /// </summary>
    /// <param name="values">Boolean values</param>
    /// <returns>Packed byte array</returns>
    protected static byte[] PackBooleans(bool[] values)
    {
        var byteCount = (values.Length + 7) / 8;
        var result = new byte[byteCount];

        for (int i = 0; i < values.Length; i++)
        {
            if (values[i])
            {
                var byteIndex = i / 8;
                var bitIndex = i % 8;
                result[byteIndex] |= (byte)(1 << bitIndex);
            }
        }

        return result;
    }

    /// <summary>
    /// Converts byte array to boolean array for coil operations
    /// </summary>
    /// <param name="bytes">Packed byte array</param>
    /// <param name="count">Number of booleans to extract</param>
    /// <returns>Boolean array</returns>
    public static bool[] UnpackBooleans(byte[] bytes, int count)
    {
        var result = new bool[count];

        for (int i = 0; i < count; i++)
        {
            var byteIndex = i / 8;
            var bitIndex = i % 8;
            result[i] = (bytes[byteIndex] & (1 << bitIndex)) != 0;
        }

        return result;
    }
}

/// <summary>
/// Represents parsed Modbus response data
/// </summary>
public class ModbusResponseData
{
    /// <summary>
    /// Transaction ID from the response
    /// </summary>
    public ushort TransactionId { get; set; }

    /// <summary>
    /// Unit ID (Slave ID) from the response
    /// </summary>
    public byte UnitId { get; set; }

    /// <summary>
    /// Function code from the response
    /// </summary>
    public byte FunctionCode { get; set; }

    /// <summary>
    /// Whether this is an error response
    /// </summary>
    public bool IsError { get; set; }

    /// <summary>
    /// Exception code if this is an error response
    /// </summary>
    public byte? ExceptionCode { get; set; }

    /// <summary>
    /// Data payload from the response
    /// </summary>
    public byte[]? Data { get; set; }

    /// <summary>
    /// Raw response bytes
    /// </summary>
    public byte[]? RawResponse { get; set; }
}
