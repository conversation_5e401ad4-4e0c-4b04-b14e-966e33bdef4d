using Microsoft.Extensions.Logging;
using Ngp.Communication.ModbusTcpMaster.FluentApi;
using Ngp.Communication.ModbusTcpMaster.Models;
using Ngp.Communication.ModbusTcpMaster.Configuration;
using Xunit;

namespace Ngp.Communication.ModbusTcpMaster.Tests;

/// <summary>
/// Unit tests for ModbusMaster functionality
/// </summary>
public class ModbusMasterTests
{
    /// <summary>
    /// Test that ModbusMaster can be created with default configuration
    /// </summary>
    [Fact]
    public void CreateDefault_ShouldCreateModbusMaster()
    {
        // Act
        using var master = ModbusMaster.CreateDefault();

        // Assert
        Assert.NotNull(master);
    }

    /// <summary>
    /// Test that ModbusMaster can be created with custom configuration
    /// </summary>
    [Fact]
    public void CreateWithCustomConfiguration_ShouldCreateModbusMaster()
    {
        // Act
        using var master = ModbusMaster.Create(builder => builder
            .WithTimeouts(connectionTimeoutMs: 10000, requestTimeoutMs: 8000)
            .WithMaxConnections(100)
            .WithParallelProcessing(true));

        // Assert
        Assert.NotNull(master);
    }

    /// <summary>
    /// Test that ModbusMaster can be created with logging
    /// </summary>
    [Fact]
    public void CreateWithLogging_ShouldCreateModbusMaster()
    {
        // Arrange
        using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());

        // Act
        using var master = ModbusMaster.CreateWithLogging(loggerFactory);

        // Assert
        Assert.NotNull(master);
    }

    /// <summary>
    /// Test that high-performance ModbusMaster can be created
    /// </summary>
    [Fact]
    public void CreateHighPerformance_ShouldCreateModbusMaster()
    {
        // Arrange
        using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());

        // Act
        using var master = ModbusMaster.CreateHighPerformance(maxConnections: 500, loggerFactory);

        // Assert
        Assert.NotNull(master);
    }

    /// <summary>
    /// Test endpoint creation
    /// </summary>
    [Fact]
    public void ModbusEndpoint_Create_ShouldCreateValidEndpoint()
    {
        // Act
        var endpoint = ModbusEndpoint.Create("*************", 502);

        // Assert
        Assert.NotNull(endpoint);
        Assert.Equal("*************", endpoint.IpAddress.ToString());
        Assert.Equal(502, endpoint.Port);
        Assert.Equal("*************:502", endpoint.ToString());
    }

    /// <summary>
    /// Test connection status for non-connected endpoint
    /// </summary>
    [Fact]
    public void GetConnectionStatus_ForNonConnectedEndpoint_ShouldReturnDisconnected()
    {
        // Arrange
        using var master = ModbusMaster.CreateDefault();
        var endpoint = ModbusEndpoint.Create("*************", 502);

        // Act
        var status = master.GetConnectionStatus(endpoint);

        // Assert
        Assert.Equal(ConnectionStatus.Disconnected, status);
    }

    /// <summary>
    /// Test FluentAPI request builder
    /// </summary>
    [Fact]
    public void ModbusRequest_FluentAPI_ShouldCreateValidRequests()
    {
        // Arrange
        var endpoint = ModbusEndpoint.Create("*************", 502);

        // Act
        var readCoilsRequest = ModbusRequest
            .ForSlave(endpoint, slaveId: 1)
            .WithTimeout(5000)
            .ReadCoils(startAddress: 0, count: 10);

        var readRegistersRequest = ModbusRequest
            .ForSlave(endpoint, slaveId: 1)
            .ReadHoldingRegisters(startAddress: 0, count: 5);

        var writeSingleCoilRequest = ModbusRequest
            .ForSlave(endpoint, slaveId: 1)
            .WriteSingleCoil(address: 0, value: true);

        var writeMultipleRegistersRequest = ModbusRequest
            .ForSlave(endpoint, slaveId: 1)
            .WriteMultipleRegisters(startAddress: 0, values: new ushort[] { 100, 200, 300 });

        // Assert
        Assert.NotNull(readCoilsRequest);
        Assert.Equal(ModbusRegisterType.Coil, readCoilsRequest.RegisterType);
        Assert.Equal(0, readCoilsRequest.StartAddress);
        Assert.Equal(10, readCoilsRequest.Count);
        Assert.Equal(5000, readCoilsRequest.TimeoutMs);

        Assert.NotNull(readRegistersRequest);
        Assert.Equal(ModbusRegisterType.HoldingRegister, readRegistersRequest.RegisterType);

        Assert.NotNull(writeSingleCoilRequest);
        Assert.Equal(ModbusRegisterType.Coil, writeSingleCoilRequest.RegisterType);
        Assert.True(writeSingleCoilRequest.Value);

        Assert.NotNull(writeMultipleRegistersRequest);
        Assert.Equal(3, writeMultipleRegistersRequest.Values.Length);
        Assert.Equal(100, writeMultipleRegistersRequest.Values[0]);
    }

    /// <summary>
    /// Test register range creation
    /// </summary>
    [Fact]
    public void RegisterRange_Creation_ShouldCreateValidRange()
    {
        // Act
        var range = new RegisterRange
        {
            RegisterType = ModbusRegisterType.HoldingRegister,
            StartAddress = 0,
            Count = 10,
            SlaveId = 1,
            PollingIntervalMs = 1000
        };

        // Assert
        Assert.NotNull(range);
        Assert.Equal(ModbusRegisterType.HoldingRegister, range.RegisterType);
        Assert.Equal(0, range.StartAddress);
        Assert.Equal(10, range.Count);
        Assert.Equal(1, range.SlaveId);
        Assert.Equal(1000, range.PollingIntervalMs);
    }

    /// <summary>
    /// Test configuration builder
    /// </summary>
    [Fact]
    public void ModbusConfiguration_ShouldHaveDefaultValues()
    {
        // Act
        var config = new ModbusConfiguration();

        // Assert
        Assert.Equal(5000, config.DefaultConnectionTimeoutMs);
        Assert.Equal(5000, config.DefaultRequestTimeoutMs);
        Assert.Equal(10, config.RequestGapMs);
        Assert.Equal(1000, config.MaxConcurrentConnections);
        Assert.Equal(10, config.MaxConcurrentRequestsPerConnection);
        Assert.True(config.EnableParallelProcessing);
        Assert.True(config.EnableConnectionPooling);
        Assert.True(config.EnableAutoReconnection);
    }

    /// <summary>
    /// Test protocol type enumeration
    /// </summary>
    [Fact]
    public void ModbusProtocolType_ShouldHaveCorrectValues()
    {
        // Assert
        Assert.Equal(0, (int)ModbusProtocolType.ModbusTcp);
        Assert.Equal(1, (int)ModbusProtocolType.ModbusRtuOverTcp);
    }

    /// <summary>
    /// Test register type enumeration
    /// </summary>
    [Fact]
    public void ModbusRegisterType_ShouldHaveCorrectValues()
    {
        // Assert
        Assert.Equal(0, (int)ModbusRegisterType.Coil);
        Assert.Equal(1, (int)ModbusRegisterType.DiscreteInput);
        Assert.Equal(2, (int)ModbusRegisterType.HoldingRegister);
        Assert.Equal(3, (int)ModbusRegisterType.InputRegister);
    }

    /// <summary>
    /// Test connection status enumeration
    /// </summary>
    [Fact]
    public void ConnectionStatus_ShouldHaveCorrectValues()
    {
        // Assert
        Assert.Equal(0, (int)ConnectionStatus.Disconnected);
        Assert.Equal(1, (int)ConnectionStatus.Connecting);
        Assert.Equal(2, (int)ConnectionStatus.Connected);
        Assert.Equal(3, (int)ConnectionStatus.Error);
        Assert.Equal(4, (int)ConnectionStatus.Disconnecting);
    }
}

/// <summary>
/// Integration tests for ModbusMaster (requires actual Modbus device or simulator)
/// </summary>
public class ModbusMasterIntegrationTests
{
    private const string TestDeviceIp = "127.0.0.1"; // Change to actual device IP
    private const int TestDevicePort = 502;

    /// <summary>
    /// Test connection to a Modbus device (skipped if no device available)
    /// </summary>
    [Fact(Skip = "Requires actual Modbus device or simulator")]
    public async Task ConnectAsync_WithValidEndpoint_ShouldConnect()
    {
        // Arrange
        using var master = ModbusMaster.CreateDefault();
        var endpoint = ModbusEndpoint.Create(TestDeviceIp, TestDevicePort);

        // Act
        var connected = await master.ConnectAsync(endpoint);

        // Assert
        Assert.True(connected);

        // Cleanup
        await master.DisconnectAsync(endpoint);
    }

    /// <summary>
    /// Test reading holding registers (skipped if no device available)
    /// </summary>
    [Fact(Skip = "Requires actual Modbus device or simulator")]
    public async Task ReadHoldingRegistersAsync_WithValidRequest_ShouldReturnData()
    {
        // Arrange
        using var master = ModbusMaster.CreateDefault();
        var endpoint = ModbusEndpoint.Create(TestDeviceIp, TestDevicePort);
        await master.ConnectAsync(endpoint);

        var request = ModbusRequest
            .ForSlave(endpoint, slaveId: 1)
            .ReadHoldingRegisters(startAddress: 0, count: 5);

        // Act
        var response = await master.ReadHoldingRegistersAsync(request);

        // Assert
        Assert.NotNull(response);
        // Note: Success depends on actual device configuration

        // Cleanup
        await master.DisconnectAsync(endpoint);
    }
}
