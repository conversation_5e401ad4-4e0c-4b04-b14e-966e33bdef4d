using System.Net;

namespace Ngp.Communication.ModbusTcpMaster.Models;

/// <summary>
/// Represents a Modbus endpoint (IP address and port combination)
/// </summary>
public record ModbusEndpoint(IPAddress IpAddress, int Port)
{
    /// <summary>
    /// Creates a new ModbusEndpoint from IP string and port
    /// </summary>
    /// <param name="ipAddress">IP address as string</param>
    /// <param name="port">Port number</param>
    /// <returns>ModbusEndpoint instance</returns>
    public static ModbusEndpoint Create(string ipAddress, int port)
    {
        return new ModbusEndpoint(IPAddress.Parse(ipAddress), port);
    }

    /// <summary>
    /// Gets the string representation of the endpoint
    /// </summary>
    /// <returns>String in format "IP:Port"</returns>
    public override string ToString() => $"{IpAddress}:{Port}";
}

/// <summary>
/// Represents connection status
/// </summary>
public enum ConnectionStatus
{
    /// <summary>
    /// Connection is disconnected
    /// </summary>
    Disconnected,

    /// <summary>
    /// Connection is in progress
    /// </summary>
    Connecting,

    /// <summary>
    /// Connection is established and healthy
    /// </summary>
    Connected,

    /// <summary>
    /// Connection has encountered an error
    /// </summary>
    Error,

    /// <summary>
    /// Connection is being disconnected
    /// </summary>
    Disconnecting
}

/// <summary>
/// Represents Modbus protocol type
/// </summary>
public enum ModbusProtocolType
{
    /// <summary>
    /// Standard Modbus TCP protocol
    /// </summary>
    ModbusTcp,

    /// <summary>
    /// Modbus RTU over TCP protocol
    /// </summary>
    ModbusRtuOverTcp
}

/// <summary>
/// Represents Modbus register types
/// </summary>
public enum ModbusRegisterType
{
    /// <summary>
    /// Coil registers (read/write, 1 bit each)
    /// </summary>
    Coil,

    /// <summary>
    /// Discrete input registers (read-only, 1 bit each)
    /// </summary>
    DiscreteInput,

    /// <summary>
    /// Holding registers (read/write, 16 bits each)
    /// </summary>
    HoldingRegister,

    /// <summary>
    /// Input registers (read-only, 16 bits each)
    /// </summary>
    InputRegister
}

/// <summary>
/// Base class for all Modbus requests
/// </summary>
public abstract record ModbusRequestBase
{
    /// <summary>
    /// The endpoint to send the request to
    /// </summary>
    public required ModbusEndpoint Endpoint { get; init; }

    /// <summary>
    /// The slave ID (unit identifier)
    /// </summary>
    public required byte SlaveId { get; init; }

    /// <summary>
    /// Request timeout in milliseconds
    /// </summary>
    public int TimeoutMs { get; init; } = 5000;

    /// <summary>
    /// Protocol type to use for this request
    /// </summary>
    public ModbusProtocolType ProtocolType { get; init; } = ModbusProtocolType.ModbusTcp;
}

/// <summary>
/// Represents a Modbus read request
/// </summary>
public record ModbusReadRequest : ModbusRequestBase
{
    /// <summary>
    /// The starting address to read from
    /// </summary>
    public required ushort StartAddress { get; init; }

    /// <summary>
    /// The number of registers/coils to read
    /// </summary>
    public required ushort Count { get; init; }

    /// <summary>
    /// The type of register to read
    /// </summary>
    public required ModbusRegisterType RegisterType { get; init; }
}

/// <summary>
/// Represents a Modbus single write request
/// </summary>
/// <typeparam name="T">The type of value to write</typeparam>
public record ModbusWriteSingleRequest<T> : ModbusRequestBase
{
    /// <summary>
    /// The address to write to
    /// </summary>
    public required ushort Address { get; init; }

    /// <summary>
    /// The value to write
    /// </summary>
    public required T Value { get; init; }

    /// <summary>
    /// The type of register to write
    /// </summary>
    public required ModbusRegisterType RegisterType { get; init; }
}

/// <summary>
/// Represents a Modbus multiple write request
/// </summary>
/// <typeparam name="T">The type of values to write</typeparam>
public record ModbusWriteMultipleRequest<T> : ModbusRequestBase
{
    /// <summary>
    /// The starting address to write to
    /// </summary>
    public required ushort StartAddress { get; init; }

    /// <summary>
    /// The values to write
    /// </summary>
    public required T[] Values { get; init; }

    /// <summary>
    /// The type of register to write
    /// </summary>
    public required ModbusRegisterType RegisterType { get; init; }
}

/// <summary>
/// Base class for all Modbus responses
/// </summary>
public record ModbusResponse
{
    /// <summary>
    /// Indicates if the request was successful
    /// </summary>
    public required bool IsSuccess { get; init; }

    /// <summary>
    /// Error message if the request failed
    /// </summary>
    public string? ErrorMessage { get; init; }

    /// <summary>
    /// Modbus exception code if applicable
    /// </summary>
    public byte? ExceptionCode { get; init; }

    /// <summary>
    /// The time when the response was received
    /// </summary>
    public DateTime Timestamp { get; init; } = DateTime.UtcNow;

    /// <summary>
    /// The round-trip time for the request
    /// </summary>
    public TimeSpan RoundTripTime { get; init; }
}

/// <summary>
/// Represents a Modbus response with data
/// </summary>
/// <typeparam name="T">The type of data returned</typeparam>
public record ModbusResponse<T> : ModbusResponse
{
    /// <summary>
    /// The data returned from the request
    /// </summary>
    public T? Data { get; init; }
}

/// <summary>
/// Represents a register range for polling
/// </summary>
public record RegisterRange
{
    /// <summary>
    /// The type of register
    /// </summary>
    public required ModbusRegisterType RegisterType { get; init; }

    /// <summary>
    /// The starting address
    /// </summary>
    public required ushort StartAddress { get; init; }

    /// <summary>
    /// The number of registers to read
    /// </summary>
    public required ushort Count { get; init; }

    /// <summary>
    /// The slave ID
    /// </summary>
    public required byte SlaveId { get; init; }

    /// <summary>
    /// Polling interval in milliseconds
    /// </summary>
    public int PollingIntervalMs { get; init; } = 1000;
}
